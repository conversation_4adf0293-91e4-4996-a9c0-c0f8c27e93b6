/**
 * Debug utility to check environment variables
 */

export function debugMinioEnv() {
  console.log("=== MinIO Environment Variables Debug ===");
  console.log("MINIO_ENDPOINT:", process.env.MINIO_ENDPOINT);
  console.log("MINIO_PORT:", process.env.MINIO_PORT);
  console.log("MINIO_USE_SSL:", process.env.MINIO_USE_SSL);
  console.log("MINIO_ACCESS_KEY:", process.env.MINIO_ACCESS_KEY);
  console.log("MINIO_SECRET_KEY:", process.env.MINIO_SECRET_KEY ? "[HIDDEN]" : "undefined");
  console.log("MINIO_BUCKET_NAME:", process.env.MINIO_BUCKET_NAME);
  console.log("==========================================");
}

export function getMinioConfig() {
  return {
    endpoint: process.env.MINIO_ENDPOINT || "localhost",
    port: process.env.MINIO_PORT || "9000",
    useSSL: process.env.MINIO_USE_SSL === "true",
    accessKey: process.env.MINIO_ACCESS_KEY || "minioadmin",
    secretKey: process.env.MINIO_SECRET_KEY || "minioadmin123",
    bucketName: process.env.MINIO_BUCKET_NAME || "ns-shop-media",
  };
}
