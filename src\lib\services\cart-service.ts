"use client";

import { api } from "@/lib/api-client";
import { Cart, CartItem, ApiResponse } from "@/types";

export interface AddToCartData {
  productId: string;
  quantity: number;
}

export interface UpdateCartItemData {
  quantity: number;
}

export interface CartSummary {
  subtotal: number;
  shipping: number;
  tax: number;
  total: number;
  itemCount: number;
  totalQuantity: number;
}

export class CartService {
  private static readonly ENDPOINT = "/cart";

  // Get user's cart
  static async getCart(): Promise<ApiResponse<Cart>> {
    return api.getById<Cart>(this.ENDPOINT, "");
  }

  // Add item to cart
  static async addToCart(data: AddToCartData): Promise<ApiResponse<Cart>> {
    return api.create<Cart>(this.ENDPOINT, data);
  }

  // Update cart item quantity
  static async updateCartItem(
    itemId: string,
    data: UpdateCartItemData
  ): Promise<ApiResponse<Cart>> {
    return api.update<Cart>(`${this.ENDPOINT}/items`, itemId, data);
  }

  // Remove item from cart
  static async removeFromCart(itemId: string): Promise<ApiResponse<Cart>> {
    return api.remove<Cart>(`${this.ENDPOINT}/items`, itemId);
  }

  // Clear entire cart
  static async clearCart(): Promise<ApiResponse<void>> {
    return api.remove<void>(this.ENDPOINT, "clear");
  }

  // Apply coupon code
  static async applyCoupon(code: string): Promise<ApiResponse<Cart>> {
    return api.create<Cart>(`${this.ENDPOINT}/coupon`, { code });
  }

  // Remove coupon
  static async removeCoupon(): Promise<ApiResponse<Cart>> {
    return api.remove<Cart>(`${this.ENDPOINT}/coupon`, "");
  }

  // Get cart summary
  static calculateCartSummary(
    cart: Cart,
    shippingFee: number = 0,
    taxRate: number = 0
  ): CartSummary {
    if (!cart || !cart.items || cart.items.length === 0) {
      return {
        subtotal: 0,
        shipping: 0,
        tax: 0,
        total: 0,
        itemCount: 0,
        totalQuantity: 0,
      };
    }

    const subtotal = cart.items.reduce((sum, item) => {
      const price = item.product.salePrice || item.product.price;
      return sum + price * item.quantity;
    }, 0);

    const tax = subtotal * taxRate;
    const total = subtotal + shippingFee + tax;
    const itemCount = cart.items.reduce((sum, item) => sum + item.quantity, 0);
    const totalQuantity = itemCount; // Same as itemCount for now

    return {
      subtotal,
      shipping: shippingFee,
      tax,
      total,
      itemCount,
      totalQuantity,
    };
  }

  // Check if product is in cart
  static isProductInCart(cart: Cart | null, productId: string): boolean {
    if (!cart || !cart.items) return false;
    return cart.items.some((item) => item.productId === productId);
  }

  // Get cart item by product ID
  static getCartItem(cart: Cart | null, productId: string): CartItem | null {
    if (!cart || !cart.items) return null;
    return cart.items.find((item) => item.productId === productId) || null;
  }

  // Get cart item quantity
  static getCartItemQuantity(cart: Cart | null, productId: string): number {
    const item = this.getCartItem(cart, productId);
    return item ? item.quantity : 0;
  }

  // Validate cart item stock
  static validateCartItemStock(item: CartItem): {
    isValid: boolean;
    maxQuantity: number;
    message?: string;
  } {
    const { product, quantity } = item;

    if (product.status !== "ACTIVE") {
      return {
        isValid: false,
        maxQuantity: 0,
        message: "Sản phẩm không còn khả dụng",
      };
    }

    if (product.stock === 0) {
      return {
        isValid: false,
        maxQuantity: 0,
        message: "Sản phẩm đã hết hàng",
      };
    }

    if (quantity > product.stock) {
      return {
        isValid: false,
        maxQuantity: product.stock,
        message: `Chỉ còn ${product.stock} sản phẩm trong kho`,
      };
    }

    return {
      isValid: true,
      maxQuantity: product.stock,
    };
  }

  // Validate entire cart
  static validateCart(cart: Cart): {
    isValid: boolean;
    invalidItems: Array<{
      item: CartItem;
      reason: string;
      maxQuantity: number;
    }>;
  } {
    if (!cart || !cart.items || cart.items.length === 0) {
      return { isValid: true, invalidItems: [] };
    }

    const invalidItems: Array<{
      item: CartItem;
      reason: string;
      maxQuantity: number;
    }> = [];

    cart.items.forEach((item) => {
      const validation = this.validateCartItemStock(item);
      if (!validation.isValid) {
        invalidItems.push({
          item,
          reason: validation.message || "Sản phẩm không hợp lệ",
          maxQuantity: validation.maxQuantity,
        });
      }
    });

    return {
      isValid: invalidItems.length === 0,
      invalidItems,
    };
  }

  // Local storage methods for guest cart
  static getGuestCart(): CartItem[] {
    if (typeof window === "undefined") return [];

    try {
      const stored = localStorage.getItem("guestCart");
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }

  static saveGuestCart(items: CartItem[]): void {
    if (typeof window === "undefined") return;

    try {
      localStorage.setItem("guestCart", JSON.stringify(items));
    } catch (error) {
      console.error("Error saving guest cart:", error);
    }
  }

  static addToGuestCart(productId: string, quantity: number): void {
    const items = this.getGuestCart();
    const existingIndex = items.findIndex(
      (item) => item.productId === productId
    );

    if (existingIndex >= 0) {
      items[existingIndex].quantity += quantity;
    } else {
      // Note: This is a simplified version. In real implementation,
      // you'd need to fetch product data to create a proper CartItem
      items.push({
        id: `guest-${Date.now()}`,
        cartId: "guest",
        productId,
        quantity,
        product: {} as any, // Would need to be populated
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    this.saveGuestCart(items);
  }

  static removeFromGuestCart(productId: string): void {
    const items = this.getGuestCart();
    const filtered = items.filter((item) => item.productId !== productId);
    this.saveGuestCart(filtered);
  }

  static clearGuestCart(): void {
    if (typeof window === "undefined") return;
    localStorage.removeItem("guestCart");
  }

  // Merge guest cart with user cart after login
  static async mergeGuestCart(): Promise<ApiResponse<Cart> | null> {
    const guestItems = this.getGuestCart();
    if (guestItems.length === 0) return null;

    try {
      // Add each guest cart item to user cart
      for (const item of guestItems) {
        await this.addToCart({
          productId: item.productId,
          quantity: item.quantity,
        });
      }

      // Clear guest cart after successful merge
      this.clearGuestCart();

      // Return updated cart
      return this.getCart();
    } catch (error) {
      console.error("Error merging guest cart:", error);
      throw error;
    }
  }
}

export default CartService;
