import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/lib/admin-auth";
import { getMinioConfig } from "@/lib/debug-env";

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const path = searchParams.get("path");

    if (!path) {
      return NextResponse.json(
        { success: false, error: "Path is required" },
        { status: 400 }
      );
    }

    const config = getMinioConfig();
    const url = `http://${config.endpoint}:${config.port}/${config.bucketName}/${path}`;

    return NextResponse.json({
      success: true,
      url,
    });
  } catch (error) {
    console.error("Get media URL error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tạo URL" },
      { status: 500 }
    );
  }
}
