import { Metadata } from "next";
import { <PERSON><PERSON>, Footer } from "@/components/layout";
import { ModernHeroSection } from "@/components/shop/modern-hero-section";
import { ModernCategorySection } from "@/components/shop/modern-category-section";
import { ModernFeaturedProducts } from "@/components/shop/modern-featured-products";
import { ModernStatsSection } from "@/components/shop/modern-stats-section";
import { ModernBrandsSection } from "@/components/shop/modern-brands-section";
import { ModernTestimonialsSection } from "@/components/shop/modern-testimonials-section";
import { ModernNewsletterSection } from "@/components/shop/modern-newsletter-section";
import { ModernSocialSection } from "@/components/shop/modern-social-section";

export const metadata: Metadata = {
  title: "NS Shop - Thời trang trực tuyến hàng đầu Việt Nam",
  description:
    "Khám phá xu hướng thời trang mới nhất tại NS Shop. C<PERSON><PERSON> hàng thời trang tr<PERSON><PERSON> tuyến với hơn 10,000+ sản phẩm chất lư<PERSON> cao, gi<PERSON> cả hợp lý. <PERSON>ễn phí vận chuyển toàn quốc.",
  keywords: [
    "thời trang",
    "quần áo",
    "giày dép",
    "phụ kiện",
    "mua sắm trực tuyến",
    "NS Shop",
  ],
  openGraph: {
    title: "NS Shop - Thời trang trực tuyến hàng đầu Việt Nam",
    description:
      "Khám phá xu hướng thời trang mới nhất tại NS Shop. Cửa hàng thời trang trực tuyến với hơn 10,000+ sản phẩm chất lượng cao.",
    images: ["/og-image-home.jpg"],
    url: "https://nsshop.com",
  },
  alternates: {
    canonical: "https://nsshop.com",
  },
};

export default function HomePage() {
  return (
    <div className="min-h-screen flex flex-col bg-white">
      <Header />
      <main className="flex-1">
        {/* Modern Hero Section */}
        <ModernHeroSection />

        {/* Modern Categories Section */}
        <ModernCategorySection />

        {/* Modern Featured Products */}
        <ModernFeaturedProducts />

        {/* Modern Stats Section */}
        <ModernStatsSection />

        {/* Modern Brands Section */}
        <ModernBrandsSection />

        {/* Modern Testimonials */}
        <ModernTestimonialsSection />

        {/* Modern Social Section */}
        <ModernSocialSection />

        {/* Modern Newsletter */}
        <ModernNewsletterSection />
      </main>
      <Footer />
    </div>
  );
}
