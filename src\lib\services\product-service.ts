"use client";

import { api } from "@/lib/api-client";
import { Product, ApiResponse, PaginatedResponse } from "@/types";

export interface ProductFilters {
  categoryId?: string;
  brandId?: string;
  minPrice?: number;
  maxPrice?: number;
  featured?: boolean;
  status?: "ACTIVE" | "INACTIVE" | "OUT_OF_STOCK";
  tags?: string[];
  search?: string;
  sort?: "name" | "price" | "createdAt" | "avgRating" | "reviewCount";
  order?: "asc" | "desc";
}

export interface ProductListParams extends ProductFilters {
  page?: number;
  limit?: number;
}

export class ProductService {
  private static readonly ENDPOINT = "/products";

  // Get paginated products list
  static async getProducts(
    params: ProductListParams = {}
  ): Promise<PaginatedResponse<Product>> {
    return api.getList<Product>(this.ENDPOINT, {
      page: params.page,
      limit: params.limit,
      search: params.search,
      sort: params.sort,
      filter: params,
    });
  }

  // Get single product by ID
  static async getProduct(id: string): Promise<ApiResponse<Product>> {
    return api.getById<Product>(this.ENDPOINT, id);
  }

  // Get product by slug
  static async getProductBySlug(slug: string): Promise<ApiResponse<Product>> {
    return api.getById<Product>(`${this.ENDPOINT}/slug`, slug);
  }

  // Get featured products
  static async getFeaturedProducts(
    limit: number = 8
  ): Promise<PaginatedResponse<Product>> {
    return api.getList<Product>(this.ENDPOINT, {
      featured: true,
      limit,
      sort: "createdAt",
      order: "desc",
    });
  }

  // Get trending products (high rating + recent)
  static async getTrendingProducts(
    limit: number = 8
  ): Promise<PaginatedResponse<Product>> {
    return api.getList<Product>(this.ENDPOINT, {
      limit,
      sort: "avgRating",
      filter: {
        order: "desc",
      },
    });
  }

  // Get products by category
  static async getProductsByCategory(
    categoryId: string,
    params: Omit<ProductListParams, "categoryId"> = {}
  ): Promise<PaginatedResponse<Product>> {
    return api.getList<Product>(this.ENDPOINT, {
      page: params.page,
      limit: params.limit,
      search: params.search,
      sort: params.sort,
      filter: {
        ...params,
        categoryId,
      },
    });
  }

  // Get products by brand
  static async getProductsByBrand(
    brandId: string,
    params: Omit<ProductListParams, "brandId"> = {}
  ): Promise<PaginatedResponse<Product>> {
    return api.getList<Product>(this.ENDPOINT, {
      page: params.page,
      limit: params.limit,
      search: params.search,
      sort: params.sort,
      filter: {
        ...params,
        brandId,
      },
    });
  }

  // Search products
  static async searchProducts(
    query: string,
    filters: ProductFilters = {},
    page: number = 1,
    limit: number = 20
  ): Promise<PaginatedResponse<Product>> {
    return api.getList<Product>(this.ENDPOINT, {
      search: query,
      page,
      limit,
      ...filters,
    });
  }

  // Get related products
  static async getRelatedProducts(
    productId: string,
    limit: number = 4
  ): Promise<PaginatedResponse<Product>> {
    return api.getList<Product>(`${this.ENDPOINT}/related`, {
      productId,
      limit,
    });
  }

  // Get recently viewed products (from localStorage)
  static getRecentlyViewed(): Product[] {
    if (typeof window === "undefined") return [];

    try {
      const stored = localStorage.getItem("recentlyViewed");
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }

  // Add product to recently viewed
  static addToRecentlyViewed(product: Product): void {
    if (typeof window === "undefined") return;

    try {
      const recent = this.getRecentlyViewed();
      const filtered = recent.filter((p) => p.id !== product.id);
      const updated = [product, ...filtered].slice(0, 10); // Keep last 10
      localStorage.setItem("recentlyViewed", JSON.stringify(updated));
    } catch (error) {
      console.error("Error saving to recently viewed:", error);
    }
  }

  // Clear recently viewed
  static clearRecentlyViewed(): void {
    if (typeof window === "undefined") return;
    localStorage.removeItem("recentlyViewed");
  }

  // Admin methods (if needed)
  static async createProduct(
    data: Partial<Product>
  ): Promise<ApiResponse<Product>> {
    return api.create<Product>(this.ENDPOINT, data);
  }

  static async updateProduct(
    id: string,
    data: Partial<Product>
  ): Promise<ApiResponse<Product>> {
    return api.update<Product>(this.ENDPOINT, id, data);
  }

  static async deleteProduct(id: string): Promise<ApiResponse<void>> {
    return api.remove<void>(this.ENDPOINT, id);
  }

  // Utility methods
  static getProductPrice(product: Product): number {
    return product.salePrice || product.price;
  }

  static getProductDiscount(product: Product): number {
    if (!product.salePrice) return 0;
    return Math.round(
      ((product.price - product.salePrice) / product.price) * 100
    );
  }

  static isProductOnSale(product: Product): boolean {
    return !!product.salePrice && product.salePrice < product.price;
  }

  static isProductInStock(product: Product): boolean {
    return product.status === "ACTIVE" && product.stock > 0;
  }

  static getProductMainImage(product: Product): string | null {
    // Try new media relationship first
    if (product.media && product.media.length > 0) {
      const primaryImage = product.media.find((m) => m.isPrimary);
      if (primaryImage) return primaryImage.media.url;
      return product.media[0].media.url;
    }

    // Fallback to legacy images
    if (product.images && product.images.length > 0) {
      return product.images[0];
    }

    return null;
  }

  static getProductImages(product: Product): string[] {
    // Try new media relationship first
    if (product.media && product.media.length > 0) {
      return product.media
        .sort((a, b) => a.order - b.order)
        .map((m) => m.media.url);
    }

    // Fallback to legacy images
    return product.images || [];
  }
}

export default ProductService;
