"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star, Quote } from "lucide-react";
import { useState, useEffect } from "react";

const testimonials = [
  {
    id: 1,
    name: "Nguyễn <PERSON>",
    avatar: "/images/avatars/user-1.svg",
    role: "<PERSON>h<PERSON>ch hàng thân thiết",
    rating: 5,
    content:
      "Chất lượng sản phẩm tuyệt vời, giao hàng nhanh chóng. Tôi đã mua nhiều lần và luôn hài lòng với dịch vụ của NS Shop.",
    product: "Áo thun cotton premium",
    date: "2024-01-15",
    verified: true,
  },
  {
    id: 2,
    name: "Trầ<PERSON> Văn <PERSON>",
    avatar: "/images/avatars/user-2.svg",
    role: "<PERSON>h<PERSON><PERSON> hàng mới",
    rating: 5,
    content:
      "T<PERSON><PERSON><PERSON> kế website đẹp, dễ sử dụng. <PERSON><PERSON><PERSON> phẩm đúng nh<PERSON> mô tả, đ<PERSON><PERSON> g<PERSON><PERSON> cẩn thận. Sẽ tiế<PERSON> tục <PERSON>ng hộ shop!",
    product: "Quần jeans skinny",
    date: "2024-01-10",
    verified: true,
  },
  {
    id: 3,
    name: "Lê Thị Hương",
    avatar: "/images/avatars/user-3.svg",
    role: "Fashion blogger",
    rating: 5,
    content:
      "NS Shop có những xu hướng thời trang mới nhất. Tôi thường xuyên tìm thấy những món đồ độc đáo ở đây.",
    product: "Váy maxi hoa nhí",
    date: "2024-01-08",
    verified: true,
  },
  {
    id: 4,
    name: "Phạm Đức Anh",
    avatar: "/images/avatars/user-4.svg",
    role: "Khách hàng VIP",
    rating: 5,
    content:
      "Dịch vụ chăm sóc khách hàng tuyệt vời. Nhân viên tư vấn nhiệt tình, giải đáp mọi thắc mắc một cách chi tiết.",
    product: "Áo khoác denim",
    date: "2024-01-05",
    verified: true,
  },
  {
    id: 5,
    name: "Võ Thị Mai",
    avatar: "/images/avatars/user-5.svg",
    role: "Khách hàng thường xuyên",
    rating: 5,
    content:
      "Giá cả hợp lý, chất lượng tốt. Đặc biệt là có nhiều chương trình khuyến mãi hấp dẫn.",
    product: "Túi xách da thật",
    date: "2024-01-03",
    verified: true,
  },
  {
    id: 6,
    name: "Hoàng Văn Đức",
    avatar: "/images/avatars/user-6.svg",
    role: "Khách hàng mới",
    rating: 4,
    content:
      "Lần đầu mua hàng online, cảm thấy rất an tâm với quy trình đặt hàng và thanh toán của NS Shop.",
    product: "Giày sneaker",
    date: "2024-01-01",
    verified: true,
  },
];

export function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Auto-rotate testimonials
  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentIndex(
        (prev) => (prev + 1) % Math.ceil(testimonials.length / 3)
      );
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const getVisibleTestimonials = () => {
    const start = currentIndex * 3;
    return testimonials.slice(start, start + 3);
  };

  return (
    <section className="py-16 lg:py-24 bg-muted/30">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center space-x-2 bg-yellow-100 dark:bg-yellow-900/20 px-4 py-2 rounded-full mb-4">
            <Star className="h-4 w-4 text-yellow-500 fill-current" />
            <span className="text-sm font-medium text-yellow-600 dark:text-yellow-400">
              Đánh giá từ khách hàng
            </span>
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            Khách hàng nói gì về{" "}
            <span className="text-fashion-600">NS Shop</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Hơn 10,000 khách hàng đã tin tưởng và lựa chọn NS Shop cho phong
            cách thời trang của họ
          </p>
        </div>

        {/* Testimonials Grid */}
        <div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 transition-all duration-500"
          onMouseEnter={() => setIsAutoPlaying(false)}
          onMouseLeave={() => setIsAutoPlaying(true)}
        >
          {getVisibleTestimonials().map((testimonial) => (
            <Card
              key={testimonial.id}
              className="group hover:shadow-lg transition-all duration-300 bg-white/80 backdrop-blur-sm border-0"
            >
              <CardContent className="p-6">
                {/* Quote Icon */}
                <div className="mb-4">
                  <Quote className="h-8 w-8 text-fashion-500/30" />
                </div>

                {/* Rating */}
                <div className="flex items-center gap-1 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${
                        i < testimonial.rating
                          ? "text-yellow-400 fill-current"
                          : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>

                {/* Content */}
                <blockquote className="text-muted-foreground mb-6 leading-relaxed">
                  "{testimonial.content}"
                </blockquote>

                {/* Product */}
                <div className="text-sm text-fashion-600 mb-4 font-medium">
                  Sản phẩm: {testimonial.product}
                </div>

                {/* Author */}
                <div className="flex items-center space-x-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage
                      src={testimonial.avatar}
                      alt={testimonial.name}
                    />
                    <AvatarFallback className="bg-fashion-100 text-fashion-600">
                      {testimonial.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-semibold text-sm">
                        {testimonial.name}
                      </h4>
                      {testimonial.verified && (
                        <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full" />
                        </div>
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {testimonial.role}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Navigation Dots */}
        <div className="flex justify-center space-x-2 mt-8">
          {Array.from({ length: Math.ceil(testimonials.length / 3) }).map(
            (_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? "bg-fashion-500 scale-110"
                    : "bg-muted-foreground/30 hover:bg-muted-foreground/50"
                }`}
              />
            )
          )}
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16 pt-16 border-t border-muted">
          <div className="text-center">
            <div className="text-3xl font-bold text-fashion-600 mb-2">4.9</div>
            <div className="text-sm text-muted-foreground">
              Đánh giá trung bình
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-fashion-600 mb-2">10k+</div>
            <div className="text-sm text-muted-foreground">
              Khách hàng hài lòng
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-fashion-600 mb-2">98%</div>
            <div className="text-sm text-muted-foreground">Tỷ lệ hài lòng</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-fashion-600 mb-2">24/7</div>
            <div className="text-sm text-muted-foreground">
              Hỗ trợ khách hàng
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
