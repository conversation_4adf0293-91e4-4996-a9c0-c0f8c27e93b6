"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@/components/layout";
import { ClientImage } from "@/components/ui/client-image";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Search,
  Grid,
  List,
  Star,
  Heart,
  ShoppingCart,
  Package,
} from "lucide-react";
import { toast } from "sonner";
import { useCategoryBySlug } from "@/hooks/use-categories";
import { useProducts } from "@/hooks/use-products";
import { useEnhancedCart } from "@/contexts/enhanced-cart-context";
import { useUserContext } from "@/contexts/user-context";
import { formatCurrency } from "@/lib/utils";

export default function CategoryPage() {
  const params = useParams();
  const slug = params.slug as string;

  // Hooks
  const { category, loading: categoryLoading } = useCategoryBySlug(slug);
  const { quickAdd, quickAddLoading } = useEnhancedCart();
  const { addToWishlist, isInWishlist } = useUserContext();

  // Local state
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [filters, setFilters] = useState({
    search: "",
    sortBy: "createdAt",
    sortOrder: "desc",
    page: 1,
    limit: 12,
  });

  // Products hook with category filter
  const {
    data: products,
    loading: productsLoading,
    total,
    totalPages,
  } = useProducts({
    ...filters,
    categoryId: category?.id,
  });

  // Handle filter changes
  const handleFilterChange = (key: string, value: string | number) => {
    setFilters({ ...filters, [key]: value, page: 1 });
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setFilters({ ...filters, page: 1 });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating)
            ? "text-yellow-400 fill-current"
            : "text-gray-300"
        }`}
      />
    ));
  };

  if (categoryLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-8" />
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {Array.from({ length: 8 }, (_, i) => (
                <Card key={i}>
                  <div className="aspect-square bg-gray-200 rounded-t-lg" />
                  <CardContent className="p-4 space-y-2">
                    <div className="h-4 bg-gray-200 rounded" />
                    <div className="h-4 bg-gray-200 rounded w-2/3" />
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (!category) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <Package className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h1 className="text-2xl font-bold mb-4">Không tìm thấy danh mục</h1>
            <Link href="/categories">
              <Button>Quay lại danh sách danh mục</Button>
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-muted-foreground mb-8">
          <Link href="/" className="hover:text-foreground">
            Trang chủ
          </Link>
          <span>/</span>
          <Link href="/categories" className="hover:text-foreground">
            Danh mục
          </Link>
          {category.parent && (
            <>
              <span>/</span>
              <Link
                href={`/categories/${category.parent.slug}`}
                className="hover:text-foreground"
              >
                {category.parent.name}
              </Link>
            </>
          )}
          <span>/</span>
          <span className="text-foreground">{category.name}</span>
        </nav>

        {/* Category Header */}
        <div className="mb-8">
          {category.image && (
            <div className="relative h-48 md:h-64 overflow-hidden rounded-lg mb-6 bg-gradient-to-br from-pink-100 to-purple-100">
              <ClientImage
                src={category.image}
                alt={category.name}
                fill
                className="object-cover"
                fallbackSrc="/images/placeholder.jpg"
              />
              <div className="absolute inset-0 bg-black/40" />
              <div className="absolute bottom-6 left-6 text-white">
                <h1 className="text-3xl md:text-4xl font-bold mb-2">
                  {category.name}
                </h1>
                {category.description && (
                  <p className="text-lg opacity-90">{category.description}</p>
                )}
              </div>
            </div>
          )}

          {!category.image && (
            <div className="mb-6">
              <h1 className="text-3xl font-bold mb-2">{category.name}</h1>
              {category.description && (
                <p className="text-muted-foreground text-lg">
                  {category.description}
                </p>
              )}
            </div>
          )}

          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <span>{total || 0} sản phẩm</span>
            {category.children && category.children.length > 0 && (
              <span>{category.children.length} danh mục con</span>
            )}
          </div>
        </div>

        {/* Subcategories */}
        {category.children && category.children.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Danh mục con</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {category.children.map((child) => (
                <Link
                  key={child.id}
                  href={`/categories/${child.slug}`}
                  className="group"
                >
                  <Card className="hover:shadow-md transition-shadow">
                    <CardContent className="p-4 text-center">
                      <div className="w-12 h-12 mx-auto mb-2 bg-gradient-to-br from-fashion-100 to-fashion-200 rounded-full flex items-center justify-center">
                        <Package className="h-6 w-6 text-fashion-500" />
                      </div>
                      <h3 className="font-medium text-sm mb-1 group-hover:text-fashion-600 transition-colors">
                        {child.name}
                      </h3>
                      <p className="text-xs text-muted-foreground">
                        {child._count?.products || 0} sản phẩm
                      </p>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        )}

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          {/* Search Bar */}
          <form onSubmit={handleSearch} className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="Tìm kiếm trong danh mục..."
                value={filters.search}
                onChange={(e) =>
                  setFilters({ ...filters, search: e.target.value })
                }
                className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              />
            </div>
            <Button type="submit" className="bg-pink-600 hover:bg-pink-700">
              Tìm kiếm
            </Button>
          </form>

          {/* Filters and View Mode */}
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex flex-wrap items-center gap-4">
              {/* Sort Filter */}
              <select
                value={`${filters.sortBy}-${filters.sortOrder}`}
                onChange={(e) => {
                  const [sortBy, sortOrder] = e.target.value.split("-");
                  handleFilterChange("sortBy", sortBy);
                  handleFilterChange("sortOrder", sortOrder);
                }}
                className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-fashion-500 focus:border-transparent"
              >
                <option value="createdAt-desc">Mới nhất</option>
                <option value="createdAt-asc">Cũ nhất</option>
                <option value="price-asc">Giá thấp đến cao</option>
                <option value="price-desc">Giá cao đến thấp</option>
                <option value="name-asc">Tên A-Z</option>
                <option value="name-desc">Tên Z-A</option>
              </select>
            </div>

            {/* View Mode Toggle */}
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("grid")}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("list")}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Products Grid/List */}
        {productsLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 8 }, (_, i) => (
              <Card key={i} className="animate-pulse">
                <div className="aspect-square bg-gray-200 rounded-t-lg" />
                <CardContent className="p-4 space-y-2">
                  <div className="h-4 bg-gray-200 rounded" />
                  <div className="h-4 bg-gray-200 rounded w-2/3" />
                  <div className="h-4 bg-gray-200 rounded w-1/2" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : products.length === 0 ? (
          <div className="text-center py-12">
            <Package className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">
              Không có sản phẩm nào
            </h2>
            <p className="text-muted-foreground mb-4">
              Danh mục này hiện chưa có sản phẩm nào
            </p>
            <Link href="/products">
              <Button>Xem tất cả sản phẩm</Button>
            </Link>
          </div>
        ) : (
          <div
            className={
              viewMode === "grid"
                ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
                : "space-y-4"
            }
          >
            {products.map((product) => (
              <Card
                key={product.id}
                className={`group hover:shadow-lg transition-shadow ${
                  viewMode === "list" ? "flex" : ""
                }`}
              >
                <Link href={`/products/${product.slug}`} className="block">
                  <div
                    className={`relative overflow-hidden ${
                      viewMode === "list"
                        ? "w-48 h-48 flex-shrink-0"
                        : "aspect-square"
                    } rounded-t-lg`}
                  >
                    <ClientImage
                      src={product.images?.[0] || "/placeholder.jpg"}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                      fallbackSrc="/images/placeholder.jpg"
                    />
                    {product.salePrice && (
                      <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
                        -
                        {Math.round(
                          ((product.price - product.salePrice) /
                            product.price) *
                            100
                        )}
                        %
                      </div>
                    )}
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button
                        size="sm"
                        variant="secondary"
                        className="h-8 w-8 p-0"
                        onClick={async (e) => {
                          e.preventDefault();
                          try {
                            await addToWishlist(product.id);
                            toast.success(
                              isInWishlist(product.id)
                                ? "Đã xóa khỏi danh sách yêu thích"
                                : "Đã thêm vào danh sách yêu thích"
                            );
                          } catch {
                            toast.error("Có lỗi xảy ra");
                          }
                        }}
                      >
                        <Heart
                          className={`h-4 w-4 ${
                            isInWishlist(product.id)
                              ? "fill-red-500 text-red-500"
                              : ""
                          }`}
                        />
                      </Button>
                    </div>
                  </div>
                </Link>

                <CardContent
                  className={`p-4 ${viewMode === "list" ? "flex-1" : ""}`}
                >
                  <Link href={`/products/${product.slug}`}>
                    <h3 className="font-medium text-sm mb-2 line-clamp-2 hover:text-pink-600 transition-colors">
                      {product.name}
                    </h3>
                  </Link>

                  <div className="flex items-center gap-1 mb-2">
                    {renderStars(product.avgRating)}
                    <span className="text-xs text-muted-foreground ml-1">
                      ({product.reviewCount})
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      {product.salePrice ? (
                        <>
                          <div className="text-lg font-bold text-fashion-600">
                            {formatCurrency(product.salePrice)}
                          </div>
                          <div className="text-sm text-muted-foreground line-through">
                            {formatCurrency(product.price)}
                          </div>
                        </>
                      ) : (
                        <div className="text-lg font-bold">
                          {formatCurrency(product.price)}
                        </div>
                      )}
                    </div>

                    <Button
                      size="sm"
                      className="bg-fashion-600 hover:bg-fashion-700 h-8 w-8 p-0"
                      disabled={quickAddLoading}
                      onClick={async (e) => {
                        e.preventDefault();
                        try {
                          await quickAdd(product.id, 1);
                          toast.success("Đã thêm vào giỏ hàng");
                        } catch {
                          toast.error("Có lỗi xảy ra khi thêm vào giỏ hàng");
                        }
                      }}
                    >
                      <ShoppingCart className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center items-center gap-2 mt-8">
            <Button
              variant="outline"
              disabled={filters.page === 1}
              onClick={() => handleFilterChange("page", filters.page - 1)}
            >
              Trước
            </Button>

            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const page = i + 1;
              return (
                <Button
                  key={page}
                  variant={filters.page === page ? "default" : "outline"}
                  onClick={() => handleFilterChange("page", page)}
                >
                  {page}
                </Button>
              );
            })}

            <Button
              variant="outline"
              disabled={filters.page === totalPages}
              onClick={() => handleFilterChange("page", filters.page + 1)}
            >
              Sau
            </Button>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
}
