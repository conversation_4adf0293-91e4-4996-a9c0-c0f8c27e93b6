"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Plus, X, Image as ImageIcon, GripVertical, Eye } from "lucide-react";
import { MediaTypeSelector } from "@/components/admin/media/MediaTypeSelector";
import { MediaField, createMediaField, isValidMediaUrl } from "@/types/media";

interface ProductImage extends MediaField {
  id?: string; // Media ID for existing images
  order?: number;
  isPrimary?: boolean;
}

interface ProductImageManagerProps {
  images: ProductImage[];
  onChange: (images: ProductImage[]) => void;
  className?: string;
  maxImages?: number;
}

export function ProductImageManager({
  images,
  onChange,
  className,
  maxImages = 10,
}: ProductImageManagerProps) {
  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);

  const handleAddImage = () => {
    if (images.length >= maxImages) return;

    const newImage: ProductImage = {
      ...createMediaField(),
      order: images.length,
      isPrimary: images.length === 0, // First image is primary by default
    };
    onChange([...images, newImage]);
  };

  const handleRemoveImage = (index: number) => {
    setDeleteIndex(index);
  };

  const confirmDelete = () => {
    if (deleteIndex !== null) {
      const newImages = images.filter((_, i) => i !== deleteIndex);
      onChange(newImages);
      setDeleteIndex(null);
    }
  };

  const moveImage = (fromIndex: number, toIndex: number) => {
    if (toIndex < 0 || toIndex >= images.length) return;

    const newImages = [...images];
    const [movedImage] = newImages.splice(fromIndex, 1);
    newImages.splice(toIndex, 0, movedImage);

    // Update order
    newImages.forEach((img, index) => {
      img.order = index;
    });

    onChange(newImages);
  };

  const handleSetPrimary = (index: number) => {
    const newImages = images.map((img, i) => ({
      ...img,
      isPrimary: i === index,
    }));
    onChange(newImages);
  };

  const getImageUrl = (image: ProductImage) => {
    return image.type === "EXTERNAL" ? image.externalUrl : image.url;
  };

  const isValidImage = (image: ProductImage) => {
    const url = getImageUrl(image);
    return url && isValidMediaUrl(url, image.type);
  };

  const isImageUrl = (url: string) => {
    if (!url) return false;
    const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".webp"];
    return imageExtensions.some((ext) => url.toLowerCase().includes(ext));
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <ImageIcon className="h-5 w-5 mr-2" />
              Hình ảnh sản phẩm ({images.length}/{maxImages})
            </CardTitle>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleAddImage}
              disabled={images.length >= maxImages}
            >
              <Plus className="h-4 w-4 mr-2" />
              Thêm ảnh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {images.length > 0 ? (
            <div className="space-y-4">
              {images.map((image, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-start gap-4">
                    {/* Drag Handle */}
                    <div className="flex flex-col gap-1 mt-2">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => moveImage(index, index - 1)}
                        disabled={index === 0}
                        className="h-6 w-6 p-0"
                      >
                        ↑
                      </Button>
                      <GripVertical className="h-4 w-4 text-muted-foreground" />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => moveImage(index, index + 1)}
                        disabled={index === images.length - 1}
                        className="h-6 w-6 p-0"
                      >
                        ↓
                      </Button>
                    </div>

                    {/* Image Configuration */}
                    <div className="flex-1 space-y-4">
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={image.isPrimary ? "default" : "secondary"}
                        >
                          {image.isPrimary ? "Ảnh chính" : `Ảnh ${index + 1}`}
                        </Badge>
                        {!image.isPrimary && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => handleSetPrimary(index)}
                          >
                            Đặt làm ảnh chính
                          </Button>
                        )}
                        <Badge
                          variant={
                            image.type === "INTERNAL" ? "default" : "outline"
                          }
                        >
                          {image.type === "INTERNAL" ? "Internal" : "External"}
                        </Badge>
                      </div>

                      <MediaTypeSelector
                        label={`Ảnh ${index + 1}`}
                        value={image}
                        onChange={(mediaField) => {
                          const updatedImages = [...images];
                          updatedImages[index] = {
                            ...updatedImages[index],
                            ...mediaField,
                          };
                          onChange(updatedImages);
                        }}
                        folder="products"
                        placeholder="Chọn ảnh sản phẩm..."
                        allowedTypes={[
                          "image/jpeg",
                          "image/png",
                          "image/gif",
                          "image/webp",
                        ]}
                      />

                      {/* Preview */}
                      {isValidImage(image) && (
                        <div className="border rounded p-2">
                          <div className="flex items-center gap-2 mb-2">
                            <Eye className="h-4 w-4" />
                            <span className="text-sm font-medium">
                              Xem trước
                            </span>
                          </div>
                          {isImageUrl(getImageUrl(image)!) ? (
                            <img
                              src={getImageUrl(image)}
                              alt={`Product image ${index + 1}`}
                              className="max-w-full h-auto max-h-32 rounded border"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = "none";
                              }}
                            />
                          ) : (
                            <div className="flex items-center justify-center h-24 bg-gray-100 rounded border">
                              <div className="text-center">
                                <ImageIcon className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                                <p className="text-sm text-gray-500">
                                  Không thể xem trước
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Remove Button */}
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveImage(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <ImageIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Chưa có hình ảnh nào</p>
              <p className="text-sm">Nhấn &quot;Thêm ảnh&quot; để bắt đầu</p>
            </div>
          )}

          {images.length > 0 && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <div className="text-sm text-blue-800">
                <strong>Lưu ý:</strong>
                <ul className="mt-1 space-y-1">
                  <li>• Ảnh đầu tiên sẽ được sử dụng làm ảnh chính</li>
                  <li>• Sử dụng nút mũi tên để thay đổi thứ tự ảnh</li>
                  <li>• Hỗ trợ tối đa {maxImages} ảnh</li>
                </ul>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={deleteIndex !== null}
        onOpenChange={() => setDeleteIndex(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận xóa ảnh</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa ảnh này? Hành động này không thể hoàn
              tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Xóa
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
