# Testing Implementation Plan - NS Shop

## Tổng quan
Tài liệu này mô tả kế hoạch triển khai testing toàn diện cho NS Shop, bao gồm unit testing, integration testing, và end-to-end testing cho các hooks, contexts, và components đã được enhanced.

## Cấu trúc Testing

### 1. Unit Testing
- **Hooks Testing**: Test các custom hooks với mock data
- **Service Layer Testing**: Test API services với mock responses
- **Utility Functions Testing**: Test các helper functions và utilities
- **Component Testing**: Test individual components với React Testing Library

### 2. Integration Testing
- **Context Integration**: Test interaction giữa contexts và hooks
- **API Integration**: Test real API calls với test database
- **Component Integration**: Test component interaction với contexts

### 3. End-to-End Testing
- **User Flows**: Test complete user journeys
- **Cart Flow**: Test add to cart, checkout process
- **Authentication Flow**: Test login, register, profile management

## Testing Framework Setup

### Dependencies
```json
{
  "devDependencies": {
    "@testing-library/react": "^14.0.0",
    "@testing-library/jest-dom": "^6.1.0",
    "@testing-library/user-event": "^14.5.0",
    "jest": "^29.7.0",
    "jest-environment-jsdom": "^29.7.0",
    "@types/jest": "^29.5.0",
    "msw": "^2.0.0"
  }
}
```

### Jest Configuration
```javascript
// jest.config.js
const nextJest = require('next/jest')

const createJestConfig = nextJest({
  dir: './',
})

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jest-environment-jsdom',
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/app/layout.tsx',
    '!src/app/globals.css',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
}

module.exports = createJestConfig(customJestConfig)
```

## Testing Categories

### 1. Hooks Testing

#### Product Hooks
- `useProducts`: Test pagination, filtering, search
- `useProductBySlug`: Test product fetching by slug
- `useRelatedProducts`: Test related products logic
- `useFeaturedProducts`: Test featured products fetching

#### Cart Hooks
- `useCart`: Test cart state management
- `useQuickAddToCart`: Test quick add functionality
- `useCartValidation`: Test cart validation logic

#### User Hooks
- `useUser`: Test user profile management
- `useOrders`: Test order history fetching
- `useAddresses`: Test address management

#### Category Hooks
- `useCategories`: Test category tree fetching
- `useCategoryBySlug`: Test category details

### 2. Context Testing

#### Enhanced Cart Context
- Cart state management
- Add/remove/update items
- Guest cart handling
- Cart validation

#### User Context
- User authentication state
- Profile management
- Wishlist management
- Address management

#### Search Context
- Search state management
- Search history
- Filter management

### 3. Component Testing

#### Shop Components
- `FeaturedProducts`: Test product display and interactions
- `TrendingProducts`: Test trending logic
- `ProductCard`: Test product card interactions
- `EnhancedProductsPage`: Test search and filtering

#### Cart Components
- Cart page functionality
- Checkout process
- Payment integration

#### User Components
- Profile page
- Order history
- Wishlist management

## Mock Data Strategy

### API Mocking với MSW
```typescript
// src/mocks/handlers.ts
import { rest } from 'msw'

export const handlers = [
  rest.get('/api/products', (req, res, ctx) => {
    return res(ctx.json(mockProducts))
  }),
  
  rest.get('/api/cart', (req, res, ctx) => {
    return res(ctx.json(mockCart))
  }),
  
  rest.post('/api/cart', (req, res, ctx) => {
    return res(ctx.json({ success: true }))
  }),
]
```

### Test Data
- Mock products với đầy đủ properties
- Mock user data với addresses và orders
- Mock cart data với items và validation
- Mock categories với hierarchy

## Test File Structure
```
src/
├── __tests__/
│   ├── hooks/
│   │   ├── use-products.test.ts
│   │   ├── use-cart.test.ts
│   │   ├── use-user.test.ts
│   │   └── use-orders.test.ts
│   ├── contexts/
│   │   ├── enhanced-cart-context.test.tsx
│   │   ├── user-context.test.tsx
│   │   └── search-context.test.tsx
│   ├── components/
│   │   ├── shop/
│   │   │   ├── featured-products.test.tsx
│   │   │   └── product-card.test.tsx
│   │   ├── cart/
│   │   │   └── cart-page.test.tsx
│   │   └── user/
│   │       └── profile-page.test.tsx
│   ├── services/
│   │   ├── product-service.test.ts
│   │   ├── cart-service.test.ts
│   │   └── user-service.test.ts
│   └── utils/
│       └── api-client.test.ts
├── mocks/
│   ├── handlers.ts
│   ├── data/
│   │   ├── products.ts
│   │   ├── users.ts
│   │   └── orders.ts
│   └── server.ts
└── test-utils/
    ├── render-with-providers.tsx
    ├── mock-router.ts
    └── test-helpers.ts
```

## Testing Best Practices

### 1. Test Organization
- Mỗi hook/component có file test riêng
- Group related tests với `describe` blocks
- Use descriptive test names

### 2. Mock Strategy
- Mock external dependencies
- Use MSW cho API calls
- Mock Next.js router và session

### 3. Assertion Strategy
- Test behavior, not implementation
- Use semantic queries từ Testing Library
- Test error states và loading states

### 4. Coverage Goals
- Minimum 80% code coverage
- 100% coverage cho critical paths
- Test edge cases và error scenarios

## Implementation Timeline

### Phase 1: Setup & Infrastructure (1-2 days)
- Setup Jest và Testing Library
- Configure MSW
- Create test utilities và helpers

### Phase 2: Hooks Testing (2-3 days)
- Test all custom hooks
- Mock API responses
- Test error handling

### Phase 3: Context Testing (2-3 days)
- Test context providers
- Test context interactions
- Test state management

### Phase 4: Component Testing (3-4 days)
- Test enhanced components
- Test user interactions
- Test integration với contexts

### Phase 5: Integration Testing (2-3 days)
- Test complete user flows
- Test API integration
- Performance testing

## Success Criteria
- All tests pass consistently
- 80%+ code coverage achieved
- Critical user flows covered
- Error scenarios tested
- Performance benchmarks met
