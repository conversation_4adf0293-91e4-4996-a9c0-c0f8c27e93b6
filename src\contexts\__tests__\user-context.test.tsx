import React from 'react';
import { renderHook, waitFor, act } from '@testing-library/react';
import { UserProvider, useUserContext } from '../user-context';
import { useUser } from '@/hooks';
import { useSession } from 'next-auth/react';

// Mock the hooks
jest.mock('@/hooks', () => ({
  useUser: jest.fn(),
}));

jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}));

const mockUseUser = useUser as jest.MockedFunction<typeof useUser>;
const mockUseSession = useSession as jest.MockedFunction<typeof useSession>;

// Mock data
const mockUser = {
  id: 'user-1',
  name: 'Test User',
  email: '<EMAIL>',
  phone: '**********',
  avatar: 'avatar.jpg',
  dateOfBirth: '1990-01-01',
  gender: 'male',
  addresses: [
    {
      id: 'address-1',
      fullName: 'Test User',
      phone: '**********',
      address: '123 Test Street',
      ward: 'Test Ward',
      district: 'Test District',
      province: 'Test Province',
      isDefault: true,
    },
  ],
  createdAt: new Date().toISOString(),
};

const mockWishlist = [
  {
    id: 'wishlist-1',
    productId: 'product-1',
    product: {
      id: 'product-1',
      name: 'Test Product',
      slug: 'test-product',
      price: 100000,
      salePrice: 80000,
      images: ['image1.jpg'],
      stock: 10,
      category: { id: '1', name: 'Test Category', slug: 'test-category' },
    },
    createdAt: new Date().toISOString(),
  },
];

const mockUserHook = {
  user: mockUser,
  loading: false,
  error: null,
  updateProfile: jest.fn(),
  addAddress: jest.fn(),
  updateAddress: jest.fn(),
  deleteAddress: jest.fn(),
  setDefaultAddress: jest.fn(),
  addToWishlist: jest.fn(),
  removeFromWishlist: jest.fn(),
  wishlist: mockWishlist,
  wishlistLoading: false,
  addresses: mockUser.addresses,
  addressesLoading: false,
  profileCompletionPercentage: 85,
  refetch: jest.fn(),
};

const mockSession = {
  user: { id: 'user-1', email: '<EMAIL>', name: 'Test User' },
  expires: '2024-12-31',
};

// Wrapper component for testing
const wrapper = ({ children }: { children: React.ReactNode }) => (
  <UserProvider>{children}</UserProvider>
);

describe('UserContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseUser.mockReturnValue(mockUserHook);
    mockUseSession.mockReturnValue({ data: mockSession, status: 'authenticated' });
  });

  it('should provide user data', async () => {
    const { result } = renderHook(() => useUserContext(), { wrapper });

    expect(result.current.user).toEqual(mockUser);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it('should provide wishlist data', async () => {
    const { result } = renderHook(() => useUserContext(), { wrapper });

    expect(result.current.wishlist).toEqual(mockWishlist);
    expect(result.current.wishlistLoading).toBe(false);
  });

  it('should provide addresses data', async () => {
    const { result } = renderHook(() => useUserContext(), { wrapper });

    expect(result.current.addresses).toEqual(mockUser.addresses);
    expect(result.current.addressesLoading).toBe(false);
  });

  it('should provide profile completion percentage', async () => {
    const { result } = renderHook(() => useUserContext(), { wrapper });

    expect(result.current.profileCompletionPercentage).toBe(85);
  });

  it('should provide user actions', async () => {
    const { result } = renderHook(() => useUserContext(), { wrapper });

    expect(typeof result.current.updateProfile).toBe('function');
    expect(typeof result.current.addAddress).toBe('function');
    expect(typeof result.current.updateAddress).toBe('function');
    expect(typeof result.current.deleteAddress).toBe('function');
    expect(typeof result.current.setDefaultAddress).toBe('function');
    expect(typeof result.current.addToWishlist).toBe('function');
    expect(typeof result.current.removeFromWishlist).toBe('function');
    expect(typeof result.current.refetch).toBe('function');
  });

  it('should handle unauthenticated user', async () => {
    mockUseSession.mockReturnValue({ data: null, status: 'unauthenticated' });
    mockUseUser.mockReturnValue({
      ...mockUserHook,
      user: null,
      wishlist: [],
      addresses: [],
    });

    const { result } = renderHook(() => useUserContext(), { wrapper });

    expect(result.current.user).toBe(null);
    expect(result.current.wishlist).toEqual([]);
    expect(result.current.addresses).toEqual([]);
  });

  it('should handle loading state', async () => {
    mockUseSession.mockReturnValue({ data: null, status: 'loading' });
    mockUseUser.mockReturnValue({ ...mockUserHook, loading: true });

    const { result } = renderHook(() => useUserContext(), { wrapper });

    expect(result.current.loading).toBe(true);
  });

  it('should handle error state', async () => {
    const errorMessage = 'Failed to load user';
    mockUseUser.mockReturnValue({ ...mockUserHook, error: errorMessage });

    const { result } = renderHook(() => useUserContext(), { wrapper });

    expect(result.current.error).toBe(errorMessage);
  });

  it('should call updateProfile', async () => {
    const { result } = renderHook(() => useUserContext(), { wrapper });

    const profileData = { name: 'Updated Name', phone: '0987654321' };

    await act(async () => {
      await result.current.updateProfile(profileData);
    });

    expect(mockUserHook.updateProfile).toHaveBeenCalledWith(profileData);
  });

  it('should call address management functions', async () => {
    const { result } = renderHook(() => useUserContext(), { wrapper });

    const addressData = {
      fullName: 'New Address',
      phone: '**********',
      address: '456 New Street',
      ward: 'New Ward',
      district: 'New District',
      province: 'New Province',
      isDefault: false,
    };

    // Test addAddress
    await act(async () => {
      await result.current.addAddress(addressData);
    });
    expect(mockUserHook.addAddress).toHaveBeenCalledWith(addressData);

    // Test updateAddress
    await act(async () => {
      await result.current.updateAddress('address-1', addressData);
    });
    expect(mockUserHook.updateAddress).toHaveBeenCalledWith('address-1', addressData);

    // Test deleteAddress
    await act(async () => {
      await result.current.deleteAddress('address-1');
    });
    expect(mockUserHook.deleteAddress).toHaveBeenCalledWith('address-1');

    // Test setDefaultAddress
    await act(async () => {
      await result.current.setDefaultAddress('address-1');
    });
    expect(mockUserHook.setDefaultAddress).toHaveBeenCalledWith('address-1');
  });

  it('should call wishlist management functions', async () => {
    const { result } = renderHook(() => useUserContext(), { wrapper });

    // Test addToWishlist
    await act(async () => {
      await result.current.addToWishlist('product-2');
    });
    expect(mockUserHook.addToWishlist).toHaveBeenCalledWith('product-2');

    // Test removeFromWishlist
    await act(async () => {
      await result.current.removeFromWishlist('product-1');
    });
    expect(mockUserHook.removeFromWishlist).toHaveBeenCalledWith('product-1');
  });

  it('should call refetch', async () => {
    const { result } = renderHook(() => useUserContext(), { wrapper });

    act(() => {
      result.current.refetch();
    });

    expect(mockUserHook.refetch).toHaveBeenCalled();
  });

  it('should throw error when used outside provider', () => {
    const { result } = renderHook(() => useUserContext());

    expect(result.error).toEqual(
      Error('useUserContext must be used within a UserProvider')
    );
  });

  it('should handle wishlist item check', async () => {
    const { result } = renderHook(() => useUserContext(), { wrapper });

    // Mock isInWishlist function
    const isInWishlist = (productId: string) => {
      return result.current.wishlist.some(item => item.productId === productId);
    };

    expect(isInWishlist('product-1')).toBe(true);
    expect(isInWishlist('product-2')).toBe(false);
  });

  it('should handle default address', async () => {
    const { result } = renderHook(() => useUserContext(), { wrapper });

    const defaultAddress = result.current.addresses.find(addr => addr.isDefault);
    expect(defaultAddress).toEqual(mockUser.addresses[0]);
  });
});
