"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Clock, Gift, Zap, ArrowRight } from "lucide-react";
import { useSettingsContext } from "@/contexts/SettingsContext";

export function PromotionBanner() {
  const { settings } = useSettingsContext();

  return (
    <section className="relative overflow-hidden bg-gradient-to-r from-fashion-500 via-fashion-600 to-fashion-700 text-white">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-black/10" />
      <div className="absolute inset-0 bg-[url('/patterns/dots.svg')] opacity-20" />
      
      <div className="container mx-auto px-4 py-8 relative z-10">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-6">
          {/* Left Content */}
          <div className="flex items-center gap-4">
            <div className="flex items-center justify-center w-12 h-12 bg-white/20 rounded-full">
              <Gift className="h-6 w-6" />
            </div>
            <div>
              <div className="flex items-center gap-2 mb-1">
                <Badge className="bg-yellow-400 text-yellow-900 hover:bg-yellow-300">
                  <Zap className="h-3 w-3 mr-1" />
                  FLASH SALE
                </Badge>
                <Badge variant="outline" className="border-white/30 text-white">
                  <Clock className="h-3 w-3 mr-1" />
                  Còn 2 ngày
                </Badge>
              </div>
              <h3 className="text-xl lg:text-2xl font-bold">
                Miễn phí vận chuyển cho đơn hàng từ {settings.shippingSettings.freeShippingThreshold.toLocaleString('vi-VN')}đ
              </h3>
              <p className="text-white/80 text-sm">
                Áp dụng cho tất cả sản phẩm thời trang mới nhất
              </p>
            </div>
          </div>

          {/* Right CTA */}
          <div className="flex flex-col sm:flex-row items-center gap-3">
            <div className="text-center sm:text-right">
              <div className="text-2xl font-bold">GIẢM 50%</div>
              <div className="text-sm text-white/80">Cho khách hàng mới</div>
            </div>
            <Button 
              asChild 
              variant="secondary" 
              size="lg" 
              className="bg-white text-fashion-600 hover:bg-white/90 font-semibold"
            >
              <Link href="/products">
                Mua ngay
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>

      {/* Animated Elements */}
      <div className="absolute top-4 right-4 w-8 h-8 bg-white/10 rounded-full animate-ping" />
      <div className="absolute bottom-4 left-4 w-6 h-6 bg-white/10 rounded-full animate-pulse" />
    </section>
  );
}
