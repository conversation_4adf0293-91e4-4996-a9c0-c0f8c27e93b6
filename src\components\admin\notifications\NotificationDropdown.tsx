"use client";

import { useState } from "react";
import Link from "next/link";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { <PERSON>, CheckCheck, ExternalLink, RefreshCw, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { useNotifications } from "@/contexts/NotificationContext";
import { NotificationItem } from "./NotificationItem";
import { cn } from "@/lib/utils";

interface NotificationDropdownProps {
  onClose?: () => void;
}

export function NotificationDropdown({ onClose }: NotificationDropdownProps) {
  const {
    notifications,
    unreadCount,
    isLoading,
    error,
    markAllAsRead,
    refreshNotifications,
  } = useNotifications();

  const [isMarkingAllRead, setIsMarkingAllRead] = useState(false);

  const handleMarkAllAsRead = async () => {
    if (unreadCount === 0) return;

    try {
      setIsMarkingAllRead(true);
      await markAllAsRead();
    } catch (err) {
      console.error("Failed to mark all as read:", err);
    } finally {
      setIsMarkingAllRead(false);
    }
  };

  const handleRefresh = () => {
    refreshNotifications();
  };

  // Sort notifications: unread first, then by creation date (newest first)
  const sortedNotifications = [...notifications].sort((a, b) => {
    // First, sort by read status (unread first)
    if (a.isRead !== b.isRead) {
      return a.isRead ? 1 : -1;
    }
    // Then sort by creation date (newest first)
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });

  const recentNotifications = sortedNotifications.slice(0, 10);
  const hasMore = notifications.length > 10;

  return (
    <div className="w-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          <h3 className="font-semibold">Thông báo</h3>
          {unreadCount > 0 && (
            <Badge variant="secondary" className="text-xs">
              {unreadCount}
            </Badge>
          )}
        </div>

        <div className="flex items-center gap-1">
          {/* Refresh Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
            className="h-8 w-8 p-0"
          >
            <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
          </Button>

          {/* Mark All Read Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleMarkAllAsRead}
            disabled={unreadCount === 0 || isMarkingAllRead}
            className="h-8 w-8 p-0"
            title="Đánh dấu tất cả đã đọc"
          >
            <CheckCheck
              className={cn("h-4 w-4", isMarkingAllRead && "animate-pulse")}
            />
          </Button>

          {/* Close Button */}
          {onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="flex flex-col h-96">
        {error ? (
          <div className="p-4 text-center">
            <p className="text-sm text-red-600 mb-2">{error}</p>
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              Thử lại
            </Button>
          </div>
        ) : isLoading ? (
          <div className="p-4 text-center">
            <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Đang tải...</p>
          </div>
        ) : recentNotifications.length === 0 ? (
          <div className="p-8 text-center flex-1 flex flex-col justify-center">
            <Bell className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">
              Không có thông báo nào
            </p>
          </div>
        ) : (
          <>
            {/* Scrollable notification list */}
            <ScrollArea className="flex-1 min-h-0">
              <div className="space-y-1 p-2">
                {recentNotifications.map((notification, index) => (
                  <div key={notification.id}>
                    <NotificationItem
                      notification={notification}
                      compact
                      onAction={onClose}
                    />
                    {index < recentNotifications.length - 1 && (
                      <Separator className="my-1" />
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>

            {/* Footer with "View All" button - always visible */}
            <div className="border-t bg-background">
              <div className="p-3">
                <Button
                  variant="ghost"
                  size="sm"
                  asChild
                  className="w-full justify-center"
                >
                  <Link href="/admin/notifications">
                    {hasMore
                      ? `Xem tất cả (${notifications.length})`
                      : "Quản lý thông báo"}
                    <ExternalLink className="h-4 w-4 ml-2" />
                  </Link>
                </Button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
