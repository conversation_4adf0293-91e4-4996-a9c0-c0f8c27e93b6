"use client";

import Link from "next/link";
import { Arrow<PERSON><PERSON>, Sparkles } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

export function HeroSection() {
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-background via-muted/20 to-background">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 pointer-events-none" />

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-fashion-200 rounded-full blur-xl opacity-30 animate-pulse" />
      <div className="absolute top-40 right-20 w-32 h-32 bg-fashion-300 rounded-full blur-xl opacity-20 animate-pulse delay-1000" />
      <div className="absolute bottom-20 left-1/4 w-16 h-16 bg-fashion-400 rounded-full blur-xl opacity-25 animate-pulse delay-2000" />

      <div className="container mx-auto px-4 py-20 lg:py-32">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <div className="inline-flex items-center space-x-2 bg-fashion-50 dark:bg-fashion-900/20 px-4 py-2 rounded-full">
                <Sparkles className="h-4 w-4 text-fashion-500" />
                <span className="text-sm font-medium text-fashion-600 dark:text-fashion-400">
                  Bộ sưu tập mới 2024
                </span>
              </div>

              <h1 className="text-4xl lg:text-6xl font-bold leading-tight">
                <span className="block">Khám phá</span>
                <span className="block animate-gradient-text bg-gradient-to-r from-fashion-500 via-fashion-600 to-fashion-700 bg-clip-text text-transparent">
                  Phong cách
                </span>
                <span className="block">Của bạn</span>
              </h1>

              <p className="text-lg text-muted-foreground max-w-md">
                Tìm kiếm những xu hướng thời trang mới nhất và tạo nên phong
                cách riêng của bạn với bộ sưu tập đa dạng từ NS Shop.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button asChild variant="fashion" size="lg" className="group">
                <Link href="/products">
                  Mua sắm ngay
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg">
                <Link href="/categories">Xem danh mục</Link>
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 pt-8">
              <div className="text-center">
                <div className="text-2xl font-bold text-fashion-600">1000+</div>
                <div className="text-sm text-muted-foreground">Sản phẩm</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-fashion-600">50+</div>
                <div className="text-sm text-muted-foreground">Thương hiệu</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-fashion-600">10k+</div>
                <div className="text-sm text-muted-foreground">Khách hàng</div>
              </div>
            </div>
          </div>

          {/* Hero Image */}
          <div className="relative">
            <div className="relative z-10">
              {/* Main Image */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-fashion-100 to-fashion-200 dark:from-fashion-900/20 dark:to-fashion-800/20">
                <div className="aspect-[4/5] bg-gradient-to-br from-fashion-200 via-fashion-300 to-fashion-400 opacity-20" />
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-6xl font-bold text-fashion-500/30">
                    NS
                  </div>
                </div>

                {/* Floating Product Cards */}
                <div className="absolute top-4 right-4 bg-white dark:bg-card rounded-lg p-3 shadow-lg animate-float">
                  <div className="w-12 h-12 bg-fashion-100 rounded-md mb-2" />
                  <div className="text-xs font-medium">Áo thun</div>
                  <div className="text-xs text-fashion-600">299.000đ</div>
                </div>

                <div className="absolute bottom-4 left-4 bg-white dark:bg-card rounded-lg p-3 shadow-lg animate-float delay-1000">
                  <div className="w-12 h-12 bg-fashion-200 rounded-md mb-2" />
                  <div className="text-xs font-medium">Váy dài</div>
                  <div className="text-xs text-fashion-600">599.000đ</div>
                </div>
              </div>
            </div>

            {/* Background Decoration */}
            <div className="absolute -top-4 -right-4 w-full h-full bg-gradient-to-br from-fashion-500/10 to-fashion-600/10 rounded-2xl -z-10" />
            <div className="absolute -bottom-4 -left-4 w-full h-full bg-gradient-to-tr from-fashion-400/10 to-fashion-500/10 rounded-2xl -z-20" />
          </div>
        </div>
      </div>
    </section>
  );
}
