Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB80270000 ntdll.dll
7FFB7F5B0000 KERNEL32.DLL
7FFB7D6C0000 KERNELBASE.dll
7FFB7F980000 USER32.dll
7FFB7DD10000 win32u.dll
7FFB7FE30000 GDI32.dll
7FFB7D420000 gdi32full.dll
7FFB7DAA0000 msvcp_win.dll
7FFB7DBF0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB7DF00000 advapi32.dll
7FFB7E0C0000 msvcrt.dll
7FFB7DFC0000 sechost.dll
7FFB7DB40000 bcrypt.dll
7FFB7F680000 RPCRT4.dll
7FFB7CA80000 CRYPTBASE.DLL
7FFB7DE80000 bcryptPrimitives.dll
7FFB7E080000 IMM32.DLL
