import { test, expect } from '@playwright/test';

test.describe('Shopping Cart Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('should display cart with initial count of 0', async ({ page }) => {
    const cartButton = page.locator('button:has-text("0")');
    await expect(cartButton).toBeVisible();
    await expect(cartButton).toContainText('0');
  });

  test('should add product to cart from homepage', async ({ page }) => {
    // Find and click first add to cart button
    const addToCartButtons = page.locator('button:has-text("Thêm vào giỏ")');
    await expect(addToCartButtons.first()).toBeVisible();
    
    await addToCartButtons.first().click();
    
    // Wait for toast notification or cart update
    await page.waitForTimeout(2000);
    
    // Check if toast notification appears
    const toastMessage = page.locator('text=Đã thêm vào giỏ hàng');
    if (await toastMessage.isVisible().catch(() => false)) {
      console.log('Toast notification appeared: Product added to cart');
    }
  });

  test('should navigate to products page and test add to cart', async ({ page }) => {
    // Navigate to products page
    await page.goto('/products');
    await page.waitForLoadState('networkidle');
    
    // Search for products
    const searchInput = page.locator('input[type="search"], input[placeholder*="tìm kiếm"], input[placeholder*="search"]').first();
    
    if (await searchInput.isVisible()) {
      await searchInput.fill('áo thun');
      await searchInput.press('Enter');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Try to add product to cart
      const addToCartButtons = page.locator('button:has-text("Thêm vào giỏ")');
      const count = await addToCartButtons.count();
      
      if (count > 0) {
        await addToCartButtons.first().click();
        await page.waitForTimeout(2000);
        
        // Check for toast notification
        const toastMessage = page.locator('text=Đã thêm vào giỏ hàng');
        if (await toastMessage.isVisible().catch(() => false)) {
          console.log('Product added to cart from products page');
        }
      } else {
        console.log('No add to cart buttons found on products page');
      }
    }
  });

  test('should test product detail page add to cart', async ({ page }) => {
    // Navigate to a specific product
    await page.goto('/products/ao-thun-cotton-premium');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Check if product page loaded
    const productTitle = page.locator('h1');
    if (await productTitle.isVisible()) {
      console.log('Product detail page loaded');
      
      // Try to add to cart
      const addToCartButton = page.locator('button:has-text("Thêm vào giỏ")');
      if (await addToCartButton.isVisible()) {
        await addToCartButton.click();
        await page.waitForTimeout(2000);
        
        // Check for login requirement message
        const loginMessage = page.locator('text=Vui lòng đăng nhập để thêm vào giỏ hàng');
        if (await loginMessage.isVisible().catch(() => false)) {
          console.log('Login required to add to cart');
        }
        
        // Check for success message
        const successMessage = page.locator('text=Đã thêm vào giỏ hàng');
        if (await successMessage.isVisible().catch(() => false)) {
          console.log('Product added to cart successfully');
        }
      }
    }
  });

  test('should test cart icon click functionality', async ({ page }) => {
    const cartButton = page.locator('button:has-text("0")');
    await expect(cartButton).toBeVisible();
    
    // Click cart button
    await cartButton.click();
    await page.waitForTimeout(1000);
    
    // Check if cart dropdown or page opens
    const cartDropdown = page.locator('[data-testid="cart-dropdown"], .cart-dropdown, .cart-sidebar');
    if (await cartDropdown.isVisible().catch(() => false)) {
      console.log('Cart dropdown opened');
    } else {
      // Check if navigated to cart page
      if (page.url().includes('/cart')) {
        console.log('Navigated to cart page');
      }
    }
  });

  test('should test wishlist functionality', async ({ page }) => {
    // Look for wishlist buttons
    const wishlistButtons = page.locator('button[aria-label*="wishlist"], button[aria-label*="favorite"], button:has(svg[data-lucide="heart"])');
    const count = await wishlistButtons.count();
    
    if (count > 0) {
      await wishlistButtons.first().click();
      await page.waitForTimeout(1000);
      console.log('Wishlist button clicked');
      
      // Check for login requirement
      const loginMessage = page.locator('text=Vui lòng đăng nhập');
      if (await loginMessage.isVisible().catch(() => false)) {
        console.log('Login required for wishlist');
      }
    } else {
      console.log('No wishlist buttons found');
    }
  });

  test('should test quantity selectors if present', async ({ page }) => {
    // Navigate to product detail page
    await page.goto('/products/ao-thun-cotton-premium');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Look for quantity selectors
    const quantityInputs = [
      page.locator('input[type="number"]'),
      page.locator('input[name*="quantity"]'),
      page.locator('.quantity-input'),
      page.locator('[data-testid*="quantity"]')
    ];
    
    let hasQuantitySelector = false;
    for (const inputGroup of quantityInputs) {
      const count = await inputGroup.count();
      if (count > 0) {
        hasQuantitySelector = true;
        
        const firstInput = inputGroup.first();
        await firstInput.fill('2');
        await expect(firstInput).toHaveValue('2');
        
        console.log('Quantity selector tested');
        break;
      }
    }
    
    if (!hasQuantitySelector) {
      // Look for quantity buttons
      const quantityButtons = [
        page.locator('button:has-text("+")'),
        page.locator('button:has-text("-")'),
        page.locator('.quantity-btn'),
        page.locator('[data-testid*="quantity"]')
      ];
      
      for (const buttonGroup of quantityButtons) {
        const count = await buttonGroup.count();
        if (count > 0) {
          hasQuantitySelector = true;
          
          const plusButton = buttonGroup.filter({ hasText: '+' }).first();
          if (await plusButton.isVisible().catch(() => false)) {
            await plusButton.click();
            console.log('Quantity plus button clicked');
          }
          break;
        }
      }
    }
    
    if (!hasQuantitySelector) {
      console.log('No quantity selectors found');
    }
  });

  test('should test size/color selectors if present', async ({ page }) => {
    // Navigate to product detail page
    await page.goto('/products/ao-thun-cotton-premium');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Look for size selectors
    const sizeSelectors = [
      page.locator('select[name*="size"]'),
      page.locator('button:has-text("S")'),
      page.locator('button:has-text("M")'),
      page.locator('button:has-text("L")'),
      page.locator('.size-selector'),
      page.locator('[data-testid*="size"]')
    ];
    
    let hasSizeSelector = false;
    for (const selectorGroup of sizeSelectors) {
      const count = await selectorGroup.count();
      if (count > 0) {
        hasSizeSelector = true;
        
        const firstSelector = selectorGroup.first();
        const tagName = await firstSelector.evaluate(el => el.tagName.toLowerCase());
        
        if (tagName === 'select') {
          await firstSelector.selectOption({ index: 1 });
        } else {
          await firstSelector.click();
        }
        
        console.log('Size selector tested');
        break;
      }
    }
    
    if (!hasSizeSelector) {
      console.log('No size selectors found');
    }
    
    // Look for color selectors
    const colorSelectors = [
      page.locator('select[name*="color"]'),
      page.locator('button[data-color]'),
      page.locator('.color-selector'),
      page.locator('[data-testid*="color"]')
    ];
    
    let hasColorSelector = false;
    for (const selectorGroup of colorSelectors) {
      const count = await selectorGroup.count();
      if (count > 0) {
        hasColorSelector = true;
        
        const firstSelector = selectorGroup.first();
        const tagName = await firstSelector.evaluate(el => el.tagName.toLowerCase());
        
        if (tagName === 'select') {
          await firstSelector.selectOption({ index: 1 });
        } else {
          await firstSelector.click();
        }
        
        console.log('Color selector tested');
        break;
      }
    }
    
    if (!hasColorSelector) {
      console.log('No color selectors found');
    }
  });

  test('should test product image gallery', async ({ page }) => {
    // Navigate to product detail page
    await page.goto('/products/ao-thun-cotton-premium');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Look for product images
    const productImages = page.locator('img[alt*="product"], img[alt*="sản phẩm"], .product-image img');
    const count = await productImages.count();
    
    if (count > 0) {
      console.log(`Found ${count} product images`);
      
      // Test clicking on images if there are multiple
      if (count > 1) {
        await productImages.nth(1).click();
        await page.waitForTimeout(1000);
        console.log('Product image clicked');
      }
    } else {
      console.log('No product images found');
    }
  });

  test('should test product reviews section', async ({ page }) => {
    // Navigate to product detail page
    await page.goto('/products/ao-thun-cotton-premium');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Look for reviews section
    const reviewsSection = [
      page.locator('text=Đánh giá'),
      page.locator('text=Reviews'),
      page.locator('.reviews'),
      page.locator('[data-testid*="review"]'),
      page.locator('text=4.8 (124)')
    ];
    
    let hasReviews = false;
    for (const section of reviewsSection) {
      if (await section.isVisible().catch(() => false)) {
        hasReviews = true;
        console.log('Reviews section found');
        break;
      }
    }
    
    if (!hasReviews) {
      console.log('No reviews section found');
    }
  });
});
