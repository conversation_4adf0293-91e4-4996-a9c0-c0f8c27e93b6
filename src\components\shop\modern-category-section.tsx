"use client";

import Link from "next/link";
import { ArrowR<PERSON>, TrendingUp } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ClientImage } from "@/components/ui/client-image";

const categories = [
  {
    id: 1,
    name: "<PERSON>o thun",
    slug: "ao-thun",
    image: "/images/categories/ao-thun.svg",
    productCount: 120,
    trending: true,
    description: "Thoải mái, phong cách",
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON> đầm",
    slug: "vay-dam",
    image: "/images/categories/vay-dam.svg",
    productCount: 85,
    trending: false,
    description: "<PERSON><PERSON> lịch, nữ tính",
  },
  {
    id: 3,
    name: "Quần jeans",
    slug: "quan-jeans",
    image: "/images/categories/quan-jeans.svg",
    productCount: 95,
    trending: true,
    description: "Bền bỉ, thời trang",
  },
  {
    id: 4,
    name: "<PERSON><PERSON>",
    slug: "ao-khoac",
    image: "/images/categories/ao-khoac.svg",
    productCount: 67,
    trending: false,
    description: "Ấm áp, phong cách",
  },
  {
    id: 5,
    name: "Phụ kiện",
    slug: "phu-kien",
    image: "/images/categories/phu-kien.svg",
    productCount: 150,
    trending: true,
    description: "Hoàn thiện phong cách",
  },
  {
    id: 6,
    name: "Giày dép",
    slug: "giay-dep",
    image: "/images/categories/giay-dep.svg",
    productCount: 78,
    trending: false,
    description: "Thoải mái, bền đẹp",
  },
];

export function ModernCategorySection() {
  return (
    <section className="py-20 lg:py-32 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-gray-100 rounded-full px-4 py-2 mb-6">
            <div className="w-2 h-2 bg-black rounded-full"></div>
            <span className="text-sm font-medium text-gray-700">
              Danh mục sản phẩm
            </span>
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Khám phá bộ sưu tập
            <br />
            <span className="text-gray-600">đa dạng của chúng tôi</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Từ thời trang công sở đến street style, chúng tôi có mọi thứ bạn cần
            để thể hiện phong cách riêng.
          </p>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {categories.map((category, index) => (
            <Link key={category.id} href={`/categories/${category.slug}`}>
              <Card className="group cursor-pointer border-0 bg-gray-50 hover:bg-white hover:shadow-xl transition-all duration-500 overflow-hidden">
                <CardContent className="p-0">
                  <div className="relative">
                    {/* Category Image */}
                    <div className="aspect-[4/3] relative overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200">
                      <ClientImage
                        src={category.image}
                        alt={category.name}
                        fill
                        className="object-cover group-hover:scale-110 transition-transform duration-700"
                        fallbackSrc="/images/placeholder.jpg"
                      />

                      {/* Overlay */}
                      <div className="absolute inset-0 bg-black/10 group-hover:bg-black/5 transition-colors duration-500" />

                      {/* Trending Badge */}
                      {category.trending && (
                        <div className="absolute top-4 left-4">
                          <Badge className="bg-black text-white border-0 px-3 py-1">
                            <TrendingUp className="h-3 w-3 mr-1" />
                            Trending
                          </Badge>
                        </div>
                      )}

                      {/* Product Count */}
                      <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1">
                        <span className="text-sm font-medium text-gray-700">
                          {category.productCount}+ sản phẩm
                        </span>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="text-xl font-bold text-gray-900 group-hover:text-black transition-colors">
                          {category.name}
                        </h3>
                        <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-black group-hover:translate-x-1 transition-all duration-300" />
                      </div>

                      <p className="text-gray-600 text-sm mb-4">
                        {category.description}
                      </p>

                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">
                          {category.productCount} sản phẩm
                        </span>
                        <div className="flex items-center gap-1 text-sm font-medium text-gray-700 group-hover:text-black transition-colors">
                          Xem tất cả
                          <ArrowRight className="h-3 w-3" />
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {/* View All Categories */}
        <div className="text-center mt-16">
          <Link
            href="/categories"
            className="inline-flex items-center gap-3 bg-black text-white px-8 py-4 rounded-full font-medium hover:bg-gray-800 transition-colors group"
          >
            Xem tất cả danh mục
            <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
          </Link>
        </div>
      </div>
    </section>
  );
}
