import { PrismaClient } from "@prisma/client";
import {
  uploadFile,
  deleteFile as deleteMinioFile,
  listFiles,
} from "@/lib/minio";
import { getMinioConfig } from "@/lib/debug-env";

const prisma = new PrismaClient();

// Helper function to normalize media URL
function normalizeMediaUrl(url: string): string {
  // If URL already contains protocol, return as is
  if (url.startsWith("http://") || url.startsWith("https://")) {
    return url;
  }

  // If URL contains undefined values, fix them
  if (url.includes("undefined")) {
    const config = getMinioConfig();
    const path = url.replace(/^.*\/ns-shop-media\//, "");
    return `http://${config.endpoint}:${config.port}/${config.bucketName}/${path}`;
  }

  return url;
}

export interface MediaCreateInput {
  filename: string;
  path: string;
  url: string;
  mimeType: string;
  size: number;
  width?: number;
  height?: number;
  alt?: string;
  title?: string;
  description?: string;
  folder?: string;
  type?: "INTERNAL" | "EXTERNAL";
  externalUrl?: string;
}

export interface MediaUpdateInput {
  alt?: string;
  title?: string;
  description?: string;
  isActive?: boolean;
}

export interface MediaQueryOptions {
  folder?: string;
  type?: "INTERNAL" | "EXTERNAL";
  mimeType?: string;
  isActive?: boolean;
  limit?: number;
  offset?: number;
  search?: string;
}

export class MediaService {
  // Create new media record
  static async create(data: MediaCreateInput) {
    try {
      const media = await prisma.media.create({
        data: {
          filename: data.filename,
          path: data.path,
          url: data.url,
          mimeType: data.mimeType,
          size: data.size,
          width: data.width,
          height: data.height,
          alt: data.alt,
          title: data.title,
          description: data.description,
          folder: data.folder || "uploads",
          type: data.type || "INTERNAL",
          externalUrl: data.externalUrl,
        },
      });

      return { success: true, data: media };
    } catch (error) {
      console.error("Error creating media:", error);
      return {
        success: false,
        error:
          error instanceof Error ? error.message : "Failed to create media",
      };
    }
  }

  // Upload file and create media record
  static async upload(
    file: Buffer,
    filename: string,
    mimeType: string,
    folder: string = "uploads",
    metadata?: {
      alt?: string;
      title?: string;
      description?: string;
      width?: number;
      height?: number;
    }
  ) {
    try {
      // Upload to MinIO
      const uploadResult = await uploadFile(file, filename, mimeType, folder);

      if (!uploadResult.success || !uploadResult.url) {
        return {
          success: false,
          error: uploadResult.error || "Upload failed",
        };
      }

      // Create media record with normalized URL
      const mediaData: MediaCreateInput = {
        filename,
        path: `${folder}/${Date.now()}-${filename}`,
        url: normalizeMediaUrl(uploadResult.url),
        mimeType,
        size: file.length,
        folder,
        type: "INTERNAL",
        ...metadata,
      };

      const media = await this.create(mediaData);
      return media;
    } catch (error) {
      console.error("Error uploading media:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Upload failed",
      };
    }
  }

  // Get media by ID
  static async getById(id: string) {
    try {
      const media = await prisma.media.findUnique({
        where: { id },
      });

      if (!media) {
        return { success: false, error: "Media not found" };
      }

      // Normalize URL before returning
      const normalizedMedia = {
        ...media,
        url: normalizeMediaUrl(media.url),
      };

      return { success: true, data: normalizedMedia };
    } catch (error) {
      console.error("Error getting media:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get media",
      };
    }
  }

  // Get media list with filters
  static async getList(options: MediaQueryOptions = {}) {
    try {
      const {
        folder,
        type,
        mimeType,
        isActive = true,
        limit = 50,
        offset = 0,
        search,
      } = options;

      const where: any = { isActive };

      if (folder) where.folder = folder;
      if (type) where.type = type;
      if (mimeType) where.mimeType = { contains: mimeType };
      if (search) {
        where.OR = [
          { filename: { contains: search, mode: "insensitive" } },
          { title: { contains: search, mode: "insensitive" } },
          { alt: { contains: search, mode: "insensitive" } },
        ];
      }

      const [media, total] = await Promise.all([
        prisma.media.findMany({
          where,
          orderBy: { createdAt: "desc" },
          take: limit,
          skip: offset,
        }),
        prisma.media.count({ where }),
      ]);

      // Normalize URLs in media items
      const normalizedMedia = media.map((item) => ({
        ...item,
        url: normalizeMediaUrl(item.url),
      }));

      return {
        success: true,
        data: {
          items: normalizedMedia,
          total,
          limit,
          offset,
        },
      };
    } catch (error) {
      console.error("Error getting media list:", error);
      return {
        success: false,
        error:
          error instanceof Error ? error.message : "Failed to get media list",
      };
    }
  }

  // Update media
  static async update(id: string, data: MediaUpdateInput) {
    try {
      const media = await prisma.media.update({
        where: { id },
        data,
      });

      // Normalize URL before returning
      const normalizedMedia = {
        ...media,
        url: normalizeMediaUrl(media.url),
      };

      return { success: true, data: normalizedMedia };
    } catch (error) {
      console.error("Error updating media:", error);
      return {
        success: false,
        error:
          error instanceof Error ? error.message : "Failed to update media",
      };
    }
  }

  // Delete media
  static async delete(id: string) {
    try {
      // Get media info first
      const media = await prisma.media.findUnique({
        where: { id },
      });

      if (!media) {
        return { success: false, error: "Media not found" };
      }

      // Delete from MinIO if it's internal media
      if (media.type === "INTERNAL") {
        await deleteMinioFile(media.url);
      }

      // Delete from database
      await prisma.media.delete({
        where: { id },
      });

      return { success: true, message: "Media deleted successfully" };
    } catch (error) {
      console.error("Error deleting media:", error);
      return {
        success: false,
        error:
          error instanceof Error ? error.message : "Failed to delete media",
      };
    }
  }

  // Get media by folder
  static async getByFolder(folder: string, limit: number = 50) {
    return this.getList({ folder, limit });
  }

  // Get product media
  static async getProductMedia(productId: string) {
    try {
      const productMedia = await prisma.productMedia.findMany({
        where: { productId },
        include: { media: true },
        orderBy: [{ isPrimary: "desc" }, { order: "asc" }],
      });

      // Normalize URLs in product media
      const normalizedProductMedia = productMedia.map((pm) => ({
        ...pm.media,
        url: normalizeMediaUrl(pm.media.url),
        order: pm.order,
        isPrimary: pm.isPrimary,
      }));

      return {
        success: true,
        data: normalizedProductMedia,
      };
    } catch (error) {
      console.error("Error getting product media:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to get product media",
      };
    }
  }

  // Add media to product
  static async addToProduct(
    productId: string,
    mediaId: string,
    order: number = 0,
    isPrimary: boolean = false
  ) {
    try {
      // If this is primary, unset other primary images
      if (isPrimary) {
        await prisma.productMedia.updateMany({
          where: { productId },
          data: { isPrimary: false },
        });
      }

      const productMedia = await prisma.productMedia.create({
        data: {
          productId,
          mediaId,
          order,
          isPrimary,
        },
        include: { media: true },
      });

      // Normalize URL in the returned data
      const normalizedProductMedia = {
        ...productMedia,
        media: {
          ...productMedia.media,
          url: normalizeMediaUrl(productMedia.media.url),
        },
      };

      return { success: true, data: normalizedProductMedia };
    } catch (error) {
      console.error("Error adding media to product:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to add media to product",
      };
    }
  }

  // Remove media from product
  static async removeFromProduct(productId: string, mediaId: string) {
    try {
      await prisma.productMedia.delete({
        where: {
          productId_mediaId: {
            productId,
            mediaId,
          },
        },
      });

      return { success: true, message: "Media removed from product" };
    } catch (error) {
      console.error("Error removing media from product:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to remove media from product",
      };
    }
  }
}
