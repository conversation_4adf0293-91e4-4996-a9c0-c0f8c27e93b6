import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/lib/admin-auth";
import { MediaService } from "@/lib/services/media.service";

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const folder = searchParams.get("folder") || undefined;
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = parseInt(searchParams.get("offset") || "0");
    const search = searchParams.get("search") || undefined;
    const type = searchParams.get("type") as
      | "INTERNAL"
      | "EXTERNAL"
      | undefined;

    // Get media from database
    const result = await MediaService.getList({
      folder,
      limit,
      offset,
      search,
      type,
    });

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data,
      });
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("List files error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tải danh sách file" },
      { status: 500 }
    );
  }
}
