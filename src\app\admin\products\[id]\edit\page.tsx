"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Save, X, Loader2, Plus } from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";
import { ProductAttributeManager } from "@/components/admin/products/ProductAttributeManager";
import { ProductImageManager } from "@/components/admin/products/ProductImageManager";
import { MediaType } from "@/types/media";

interface ProductImage {
  url: string;
  type: MediaType;
  externalUrl?: string;
}

interface Category {
  id: string;
  name: string;
  slug: string;
}

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  salePrice?: number;
  images: string[];
  imageTypes?: MediaType[];
  externalImages?: string[];
  categoryId: string;
  brandId?: string;

  stock: number;
  sku: string;
  featured: boolean;
  status: "ACTIVE" | "INACTIVE" | "OUT_OF_STOCK";
  tags: string[];
  category: {
    id: string;
    name: string;
    slug: string;
  };
  brand?: {
    id: string;
    name: string;
    logo?: string;
  };

  ProductAttribute?: Array<{
    id: string;
    attributeId: string;
    attributeValueId: string;
    attribute: {
      id: string;
      name: string;
      type: string;
    };
    attributeValue: {
      id: string;
      value: string;
    };
  }>;
}

interface ProductFormData {
  name: string;
  description: string;
  price: number;
  salePrice?: number;
  images: ProductImage[];
  categoryId: string;
  brandId?: string;

  stock: number;
  sku: string;
  featured: boolean;
  status: "ACTIVE" | "INACTIVE" | "OUT_OF_STOCK";
  tags: string[];
  attributes: Array<{ attributeId: string; attributeValueId: string }>;
}

export default function EditProductPage() {
  const router = useRouter();
  const params = useParams();
  const productId = params.id as string;

  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [categories, setCategories] = useState<Category[]>([]);
  const [product, setProduct] = useState<Product | null>(null);

  const [tagInput, setTagInput] = useState("");

  const [formData, setFormData] = useState<ProductFormData>({
    name: "",
    description: "",
    price: 0,
    salePrice: undefined,
    images: [],
    categoryId: "",
    brandId: "",

    stock: 0,
    sku: "",
    featured: false,
    status: "ACTIVE",
    tags: [],
    attributes: [],
  });

  useEffect(() => {
    if (productId) {
      fetchProduct();
      fetchCategories();
    }
  }, [productId]);

  const fetchProduct = async () => {
    try {
      const response = await fetch(`/api/admin/products/${productId}`);
      if (response.ok) {
        const response_data = await response.json();
        const data = response_data.data; // Extract the actual product data
        setProduct(data);
        setFormData({
          name: data.name,
          description: data.description,
          price: data.price,
          salePrice: data.salePrice,
          images:
            data.images
              ?.map((url: string, index: number) => {
                const imageType = data.imageTypes?.[index] || "INTERNAL";
                const externalUrl = data.externalImages?.[index] || "";

                return {
                  url: imageType === "INTERNAL" ? url : "",
                  type: imageType,
                  externalUrl: imageType === "EXTERNAL" ? externalUrl : "",
                };
              })
              .filter(
                (img: any) =>
                  (img.type === "INTERNAL" && img.url) ||
                  (img.type === "EXTERNAL" && img.externalUrl)
              ) || [],
          categoryId: data.categoryId,
          brandId: data.brandId || "",

          stock: data.stock,
          sku: data.sku,
          featured: data.featured,
          status: data.status,
          tags: data.tags || [],
          attributes:
            data.ProductAttribute?.map((pa: any) => ({
              attributeId: pa.attributeId,
              attributeValueId: pa.attributeValueId,
            })) || [],
        });
      } else {
        toast.error("Không tìm thấy sản phẩm");
        router.push("/admin/products");
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi tải sản phẩm");
    } finally {
      setInitialLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch("/api/admin/categories");
      if (response.ok) {
        const data = await response.json();
        setCategories(data.categories || []);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  const handleInputChange = (field: keyof ProductFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleAttributesChange = (
    attributes: Array<{ attributeId: string; attributeValueId: string }>
  ) => {
    setFormData((prev) => ({ ...prev, attributes }));
  };

  const addTag = () => {
    if (tagInput.trim() && !(formData.tags || []).includes(tagInput.trim())) {
      setFormData((prev) => ({
        ...prev,
        tags: [...(prev.tags || []), tagInput.trim()],
      }));
      setTagInput("");
    }
  };

  const removeTag = (tag: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: (prev.tags || []).filter((t) => t !== tag),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.description || !formData.categoryId) {
      toast.error("Vui lòng điền đầy đủ thông tin bắt buộc");
      return;
    }

    if (formData.images.length === 0) {
      toast.error("Vui lòng thêm ít nhất một hình ảnh");
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/admin/products/${productId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          // Convert ProductImage array to API format - maintain index sync
          images: formData.images.map((img) =>
            img.type === "INTERNAL" ? img.url : ""
          ),
          imageTypes: formData.images.map((img) => img.type),
          externalImages: formData.images.map((img) =>
            img.type === "EXTERNAL" ? img.externalUrl || "" : ""
          ),
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Cập nhật sản phẩm thành công");
        router.push("/admin/products");
      } else {
        toast.error(data.error || "Có lỗi xảy ra khi cập nhật sản phẩm");
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi cập nhật sản phẩm");
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Đang tải sản phẩm...</span>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold mb-4">Không tìm thấy sản phẩm</h2>
        <Link href="/admin/products">
          <Button>Quay lại danh sách</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/admin/products">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Chỉnh sửa sản phẩm</h1>
            <p className="text-muted-foreground">
              Cập nhật thông tin sản phẩm: {product?.name || "..."}
            </p>
          </div>
        </div>
        <Button
          onClick={handleSubmit}
          disabled={loading}
          className="bg-pink-600 hover:bg-pink-700"
        >
          <Save className="h-4 w-4 mr-2" />
          {loading ? "Đang lưu..." : "Lưu thay đổi"}
        </Button>
      </div>

      <form
        onSubmit={handleSubmit}
        className="grid grid-cols-1 lg:grid-cols-3 gap-6"
      >
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Info */}
          <Card>
            <CardHeader>
              <CardTitle>Thông tin cơ bản</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Tên sản phẩm *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  placeholder="Nhập tên sản phẩm"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Mô tả sản phẩm *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) =>
                    handleInputChange("description", e.target.value)
                  }
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  placeholder="Nhập mô tả chi tiết sản phẩm"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Giá bán (VND) *
                  </label>
                  <input
                    type="number"
                    value={formData.price}
                    onChange={(e) =>
                      handleInputChange("price", parseInt(e.target.value) || 0)
                    }
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                    placeholder="0"
                    min="0"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Giá khuyến mãi (VND)
                  </label>
                  <input
                    type="number"
                    value={formData.salePrice || ""}
                    onChange={(e) =>
                      handleInputChange(
                        "salePrice",
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                    placeholder="0"
                    min="0"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Images */}
          <ProductImageManager
            images={formData.images}
            onChange={(images) => handleInputChange("images", images)}
          />

          {/* Tags */}
          <Card>
            <CardHeader>
              <CardTitle>Tags</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={(e) =>
                    e.key === "Enter" && (e.preventDefault(), addTag())
                  }
                  className="flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  placeholder="Nhập tag"
                />
                <Button type="button" onClick={addTag} variant="outline">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {(formData.tags?.length || 0) > 0 && (
                <div className="flex flex-wrap gap-2">
                  {(formData.tags || []).map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center gap-1 px-3 py-1 bg-pink-100 text-pink-800 rounded-full text-sm"
                    >
                      {tag}
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="hover:text-pink-600"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status & Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Cài đặt</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Danh mục *
                </label>
                <select
                  value={formData.categoryId}
                  onChange={(e) =>
                    handleInputChange("categoryId", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  required
                >
                  <option value="">Chọn danh mục</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Trạng thái
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => handleInputChange("status", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                >
                  <option value="ACTIVE">Hoạt động</option>
                  <option value="INACTIVE">Không hoạt động</option>
                  <option value="OUT_OF_STOCK">Hết hàng</option>
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Số lượng
                  </label>
                  <input
                    type="number"
                    value={formData.stock}
                    onChange={(e) =>
                      handleInputChange("stock", parseInt(e.target.value) || 0)
                    }
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">SKU</label>
                  <input
                    type="text"
                    value={formData.sku}
                    onChange={(e) => handleInputChange("sku", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                    placeholder="SKU sản phẩm"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="featured"
                  checked={formData.featured}
                  onChange={(e) =>
                    handleInputChange("featured", e.target.checked)
                  }
                  className="rounded border-gray-300 text-pink-600 focus:ring-pink-500"
                />
                <label htmlFor="featured" className="text-sm font-medium">
                  Sản phẩm nổi bật
                </label>
              </div>
            </CardContent>
          </Card>

          {/* Product Attributes */}
          <ProductAttributeManager
            attributes={formData.attributes}
            onAttributesChange={handleAttributesChange}
          />
        </div>
      </form>
    </div>
  );
}
