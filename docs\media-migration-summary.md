# Media Migration Summary - NS Shop

## Tổng quan

Đã thực hiện migration từ hệ thống lưu trữ media đơn gi<PERSON> (string URLs) sang hệ thống quản lý media tập trung với bảng Media riêng biệt. Điều này giúp:

- Quản lý media tốt hơn với metadata đầy đủ
- Hỗ trợ cả internal và external media
- Tối ưu hóa hiệu suất và khả năng mở rộng
- Cung cấp giao diện quản lý media chuyên nghiệp

## Các thay đổi chính

### 1. Database Schema Changes

#### Bảng Media mới
```sql
CREATE TABLE "media" (
  "id" TEXT NOT NULL,
  "filename" TEXT NOT NULL,
  "path" TEXT NOT NULL,
  "url" TEXT NOT NULL,
  "mimeType" TEXT NOT NULL,
  "size" INTEGER NOT NULL,
  "width" INTEGER,
  "height" INTEGER,
  "alt" TEXT,
  "title" TEXT,
  "description" TEXT,
  "folder" TEXT NOT NULL DEFAULT 'uploads',
  "type" TEXT NOT NULL DEFAULT 'INTERNAL',
  "externalUrl" TEXT,
  "isActive" BOOLEAN NOT NULL DEFAULT true,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  PRIMARY KEY ("id")
);
```

#### Bảng ProductMedia (Many-to-Many)
```sql
CREATE TABLE "product_media" (
  "id" TEXT NOT NULL,
  "productId" TEXT NOT NULL,
  "mediaId" TEXT NOT NULL,
  "order" INTEGER NOT NULL DEFAULT 0,
  "isPrimary" BOOLEAN NOT NULL DEFAULT false,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id")
);
```

#### Bảng ReviewMedia (Many-to-Many)
```sql
CREATE TABLE "review_media" (
  "id" TEXT NOT NULL,
  "reviewId" TEXT NOT NULL,
  "mediaId" TEXT NOT NULL,
  "order" INTEGER NOT NULL DEFAULT 0,
  PRIMARY KEY ("id")
);
```

#### Cập nhật các bảng khác
- **User**: `avatar` → `avatarId` (reference to Media)
- **AdminUser**: `avatar` → `avatarId` (reference to Media)
- **Brand**: `logo`, `logoType`, `externalLogoUrl` → `logoId` (reference to Media)
- **Category**: `image` → `imageId` (reference to Media)
- **Product**: `images[]`, `imageTypes[]`, `externalImages[]` → relation với ProductMedia
- **Post**: `featuredImage`, `featuredImageType`, `externalFeaturedImage` → `featuredImageId`
- **Page**: `featuredImage` → `featuredImageId`
- **Event**: `image` → `imageId`

### 2. Backend Changes

#### MediaService mới
- `src/lib/services/media.service.ts`: Service layer cho quản lý media
- Các method: create, upload, getById, getList, update, delete
- Hỗ trợ cả internal (MinIO) và external media
- Quản lý ProductMedia relationships

#### API Routes cập nhật
- `src/app/api/admin/media/upload/route.ts`: Upload với metadata
- `src/app/api/admin/media/list/route.ts`: List với filters và pagination
- `src/app/api/admin/media/[id]/route.ts`: CRUD operations cho media
- `src/app/api/admin/products/[id]/media/route.ts`: Quản lý product media

#### Cập nhật Product API
- Xử lý media relationships khi tạo/cập nhật products
- Bao gồm media data trong response

### 3. Frontend Changes

#### MediaManager Component
- Cập nhật interface MediaFile với đầy đủ metadata
- Hỗ trợ search, filter theo folder, type, mimeType
- Grid và List view modes
- Bulk operations (delete multiple files)

#### ProductImageManager Component
- Hỗ trợ primary image selection
- Order management cho images
- Metadata fields (alt, title, description)

#### Admin Dashboard
- Trang quản lý media mới: `/admin/media`
- Cập nhật product listing để hiển thị media từ bảng mới
- Cập nhật brand listing để hiển thị logo từ bảng Media

#### Utility Functions
- `src/lib/admin/image-utils.ts`: Helper functions cho cả legacy và new format
- `getProductImage()`: Universal function cho product images
- `getBrandLogoUrl()`: Helper cho brand logos

### 4. Type Definitions

#### Cập nhật interfaces
- `Brand`: logo field từ string → object với id, url, alt
- `Product`: thêm media relationship, giữ legacy fields cho backward compatibility
- `MediaFile`: Interface mới cho media objects

### 5. Seed Data

#### Cập nhật seed.ts
- Tạo media items cho brands, products, posts, pages, events
- Tạo ProductMedia relationships
- Hỗ trợ cả internal và external media types

## Migration Process

1. **Schema Migration**: Tạo bảng Media và relationships
2. **Data Migration**: Seed data với media items
3. **API Migration**: Cập nhật endpoints để sử dụng Media service
4. **Frontend Migration**: Cập nhật components và pages
5. **Testing**: Verify functionality

## Lợi ích

### Quản lý tốt hơn
- Metadata đầy đủ (alt text, title, description)
- Thông tin kỹ thuật (size, dimensions, MIME type)
- Tổ chức theo folders
- Soft delete với isActive flag

### Hiệu suất
- Tối ưu queries với proper indexing
- Lazy loading cho media relationships
- Caching-friendly structure

### Khả năng mở rộng
- Hỗ trợ multiple media types
- Flexible relationship structure
- Easy to add new media features

### UX/UI
- Professional media management interface
- Bulk operations
- Search và filter capabilities
- Visual media browser

## Backward Compatibility

Hệ thống vẫn hỗ trợ legacy format thông qua:
- Helper functions trong `image-utils.ts`
- Optional legacy fields trong Product interface
- Graceful fallbacks trong components

## Next Steps

1. **Fix remaining API issues**: Cập nhật các API routes còn lại
2. **Add image optimization**: Implement image resizing và optimization
3. **Add CDN support**: Integrate với CDN cho better performance
4. **Add media analytics**: Track media usage và performance
5. **Add media validation**: File type và size validation
6. **Add media versioning**: Support cho multiple versions của cùng một file

## Files Changed

### Database
- `prisma/schema.prisma`: Schema updates
- `prisma/seed.ts`: Seed data updates

### Backend
- `src/lib/services/media.service.ts`: New service
- `src/app/api/admin/media/**`: Media API routes
- `src/app/api/admin/products/**`: Updated product APIs

### Frontend
- `src/components/admin/MediaManager.tsx`: Updated component
- `src/components/admin/products/ProductImageManager.tsx`: Updated component
- `src/app/admin/media/page.tsx`: New admin page
- `src/app/admin/products/page.tsx`: Updated to use new media
- `src/app/admin/brands/page.tsx`: Updated to use new media

### Utilities
- `src/lib/admin/image-utils.ts`: Helper functions
- `src/types/index.ts`: Type definitions

### Documentation
- `docs/media-migration-summary.md`: This document
