import { test, expect } from "@playwright/test";

test.describe("NS Shop Frontend Comprehensive Tests", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto("/");
    // Wait for page to load completely
    await page.waitForLoadState("networkidle");
  });

  test("should load homepage successfully", async ({ page }) => {
    await expect(page).toHaveTitle(/NS Shop/);
    await expect(page.locator("h1")).toContainText("Khám phá");
    await expect(page.locator("h1")).toContainText("Phong cách");
    await expect(page.locator("h1")).toContainText("Của bạn");
  });

  test("should display navigation menu correctly", async ({ page }) => {
    const nav = page.locator("nav");
    await expect(nav.locator('a[href="/"]')).toContainText("Trang chủ");
    await expect(nav.locator('a[href="/products"]')).toContainText("Sản phẩm");
    await expect(nav.locator('a[href="/categories"]')).toContainText(
      "Danh mục"
    );
    await expect(nav.locator('a[href="/about"]')).toContainText("Về chúng tôi");
    await expect(nav.locator('a[href="/contact"]')).toContainText("Liên hệ");
  });

  test("should display search functionality", async ({ page }) => {
    const searchBox = page.locator('input[placeholder*="Tìm kiếm"]');
    await expect(searchBox).toBeVisible();

    // Test search input
    await searchBox.fill("áo thun");
    await expect(searchBox).toHaveValue("áo thun");
  });

  test("should display cart with initial count", async ({ page }) => {
    const cartButton = page.locator('button:has-text("0")');
    await expect(cartButton).toBeVisible();
    await expect(cartButton).toContainText("0");
  });

  test("should display authentication links", async ({ page }) => {
    await expect(page.locator('a[href="/auth/signin"]')).toContainText(
      "Đăng nhập"
    );
    await expect(page.locator('a[href="/auth/signup"]')).toContainText(
      "Đăng ký"
    );
  });

  test("should display hero section with content", async ({ page }) => {
    const heroSection = page.locator("main").first();
    await expect(heroSection.locator("h1")).toContainText("Khám phá");
    await expect(heroSection.locator("h1")).toContainText("Phong cách");
    await expect(heroSection.locator("h1")).toContainText("Của bạn");
    await expect(heroSection).toContainText("Bộ sưu tập mới 2024");
    await expect(heroSection).toContainText(
      "Tìm kiếm những xu hướng thời trang mới nhất"
    );
  });

  test("should display hero action buttons", async ({ page }) => {
    const shopNowBtn = page.locator(
      'a[href="/products"]:has-text("Mua sắm ngay")'
    );
    const categoriesBtn = page.locator(
      'a[href="/categories"]:has-text("Xem danh mục")'
    );

    await expect(shopNowBtn).toBeVisible();
    await expect(categoriesBtn).toBeVisible();
  });

  test("should display statistics section", async ({ page }) => {
    await expect(page.locator("text=1000+").first()).toBeVisible();
    await expect(page.locator("text=50+").first()).toBeVisible();
    await expect(page.locator("text=Thương hiệu").first()).toBeVisible();
    await expect(page.locator("text=10k+").first()).toBeVisible();
    await expect(page.locator("text=Khách hàng").first()).toBeVisible();
  });

  test("should display categories section", async ({ page }) => {
    await expect(
      page.locator("h2").filter({ hasText: "Danh mục sản phẩm" })
    ).toBeVisible();
    await expect(
      page.locator("text=Khám phá bộ sưu tập đa dạng")
    ).toBeVisible();

    // Check category cards
    await expect(page.locator('a[href="/categories/ao-thun"]')).toBeVisible();
    await expect(page.locator('a[href="/categories/vay-dam"]')).toBeVisible();
    await expect(
      page.locator('a[href="/categories/quan-jeans"]')
    ).toBeVisible();
    await expect(page.locator('a[href="/categories/ao-khoac"]')).toBeVisible();
    await expect(page.locator('a[href="/categories/phu-kien"]')).toBeVisible();
    await expect(page.locator('a[href="/categories/giay-dep"]')).toBeVisible();
  });

  test("should display featured products section", async ({ page }) => {
    await expect(
      page.locator("h2").filter({ hasText: "Sản phẩm nổi bật" })
    ).toBeVisible();
    await expect(
      page.locator("text=Khám phá những sản phẩm được yêu thích nhất")
    ).toBeVisible();

    // Check for product cards
    const productCards = page.locator('a[href^="/products/"]');
    await expect(productCards.first()).toBeVisible();
  });

  test("should display product information correctly", async ({ page }) => {
    // Check first product
    const firstProduct = page.locator(
      'a[href="/products/ao-thun-cotton-premium"]'
    );
    await expect(firstProduct).toContainText("Áo thun cotton premium");

    // Check for price display - use first() to avoid strict mode violation
    await expect(page.locator("text=199.000 ₫").first()).toBeVisible();
    await expect(page.locator("text=299.000 ₫").first()).toBeVisible();

    // Check for ratings
    await expect(page.locator("text=4.8 (124)")).toBeVisible();
  });

  test("should have working add to cart buttons", async ({ page }) => {
    const addToCartButtons = page.locator('button:has-text("Thêm vào giỏ")');
    await expect(addToCartButtons.first()).toBeVisible();

    // Click first add to cart button
    await addToCartButtons.first().click();
    // Wait for any response
    await page.waitForTimeout(1000);
  });

  test("should display newsletter section", async ({ page }) => {
    await expect(
      page.locator("h2").filter({ hasText: "Đăng ký nhận tin" })
    ).toBeVisible();
    await expect(
      page.locator("text=Cập nhật những xu hướng thời trang mới nhất")
    ).toBeVisible();

    // Check newsletter form
    const emailInput = page.locator(
      'input[placeholder*="Địa chỉ email của bạn"]'
    );
    const subscribeBtn = page.locator(
      'button:has-text("Đăng ký ngay và nhận voucher")'
    );

    await expect(emailInput).toBeVisible();
    await expect(subscribeBtn).toBeVisible();
  });

  test("should test newsletter subscription", async ({ page }) => {
    // Use the main newsletter form
    const emailInput = page.locator(
      'input[placeholder*="Địa chỉ email của bạn"]'
    );
    const subscribeBtn = page.locator(
      'button:has-text("Đăng ký ngay và nhận voucher")'
    );

    await emailInput.fill("<EMAIL>");
    await expect(emailInput).toHaveValue("<EMAIL>");

    await subscribeBtn.click();
    await page.waitForTimeout(1000);
  });

  test("should display footer correctly", async ({ page }) => {
    // Scroll to bottom of page
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
    await page.waitForTimeout(3000);

    // Try different footer selectors
    const footer = page.locator("footer, contentinfo, [role='contentinfo']");
    await expect(footer).toBeVisible();
    await expect(footer).toContainText("NS Shop");
    await expect(footer).toContainText("Khám phá xu hướng thời trang mới nhất");

    // Check footer links
    await expect(footer.locator('a[href="/about"]').first()).toContainText(
      "Về chúng tôi"
    );
    await expect(footer.locator('a[href="/products"]').first()).toContainText(
      "Sản phẩm"
    );
    await expect(footer.locator('a[href="/categories"]').first()).toContainText(
      "Danh mục"
    );
    await expect(footer.locator('a[href="/contact"]').first()).toContainText(
      "Liên hệ"
    );

    // Check support links
    await expect(footer.locator('a[href="/shipping"]').first()).toContainText(
      "Chính sách giao hàng"
    );
    await expect(footer.locator('a[href="/returns"]').first()).toContainText(
      "Đổi trả hàng"
    );
    await expect(footer.locator('a[href="/privacy"]').first()).toContainText(
      "Chính sách bảo mật"
    );
  });

  test("should display contact information in footer", async ({ page }) => {
    // Scroll to bottom of page
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
    await page.waitForTimeout(3000);

    // Try different footer selectors
    const footer = page.locator("footer, contentinfo, [role='contentinfo']");
    await expect(footer).toBeVisible();
    await expect(footer).toContainText("123 Đường ABC, Quận 1, TP.HCM");
    await expect(footer).toContainText("+84 123 456 789");
    await expect(footer).toContainText("<EMAIL>");
  });

  test("should display copyright information", async ({ page }) => {
    // Scroll to bottom of page
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
    await page.waitForTimeout(1000);

    await expect(
      page.locator("text=© 2024 NS Shop. Tất cả quyền được bảo lưu.")
    ).toBeVisible();
  });

  test("should navigate to products page from hero button", async ({
    page,
  }) => {
    await page.locator('a[href="/products"]:has-text("Mua sắm ngay")').click();
    await page.waitForLoadState("networkidle");
    await expect(page).toHaveURL(/.*\/products/);
  });

  test("should navigate to categories page from hero button", async ({
    page,
  }) => {
    await page
      .locator('a[href="/categories"]:has-text("Xem danh mục")')
      .click({ force: true });
    await page.waitForLoadState("networkidle");
    await page.waitForTimeout(2000);
    await expect(page).toHaveURL(/.*\/categories/);
  });

  test("should navigate to category pages when clicked", async ({ page }) => {
    await page.locator('a[href="/categories/ao-thun"]').click();
    await page.waitForLoadState("networkidle");
    await page.waitForTimeout(2000);
    await expect(page).toHaveURL(/.*\/categories\/ao-thun/);
  });
});
