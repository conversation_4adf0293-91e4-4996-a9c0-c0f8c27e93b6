import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { verifyAdminToken } from "@/lib/admin/auth";

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search") || "";
    const status = searchParams.get("status") || "";
    const rating = searchParams.get("rating") || "";

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { comment: { contains: search, mode: "insensitive" } },
        { user: { name: { contains: search, mode: "insensitive" } } },
        { user: { email: { contains: search, mode: "insensitive" } } },
        { product: { name: { contains: search, mode: "insensitive" } } },
      ];
    }

    if (status && status !== "all") {
      where.status = status;
    }

    if (rating && rating !== "all") {
      where.rating = parseInt(rating);
    }

    // Get reviews with related data
    const [reviews, total] = await Promise.all([
      prisma.review.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          product: {
            select: {
              id: true,
              name: true,
              media: {
                include: {
                  media: {
                    select: {
                      id: true,
                      url: true,
                      alt: true,
                    },
                  },
                },
                take: 1,
              },
              slug: true,
            },
          },
        },
      }),
      prisma.review.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      data: reviews,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error("Error fetching reviews:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const reviewId = searchParams.get("id");

    if (!reviewId) {
      return NextResponse.json(
        { error: "Review ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { status } = body;

    if (!["PENDING", "APPROVED", "REJECTED"].includes(status)) {
      return NextResponse.json({ error: "Invalid status" }, { status: 400 });
    }

    // Update review status
    const updatedReview = await prisma.review.update({
      where: { id: reviewId },
      data: { status },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        product: {
          select: {
            id: true,
            name: true,
            media: {
              include: {
                media: {
                  select: {
                    id: true,
                    url: true,
                    alt: true,
                  },
                },
              },
              take: 1,
            },
            slug: true,
          },
        },
      },
    });

    // If approved, update product rating
    if (status === "APPROVED") {
      await updateProductRating(updatedReview.productId);
    }

    return NextResponse.json({
      data: updatedReview,
      message: `Review ${status.toLowerCase()} successfully`,
    });
  } catch (error) {
    console.error("Error updating review:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Helper function to update product rating
async function updateProductRating(productId: string) {
  try {
    const approvedReviews = await prisma.review.findMany({
      where: {
        productId,
        status: "APPROVED",
      },
      select: {
        rating: true,
      },
    });

    const totalReviews = approvedReviews.length;
    const avgRating =
      totalReviews > 0
        ? approvedReviews.reduce((sum, review) => sum + review.rating, 0) /
          totalReviews
        : 0;

    await prisma.product.update({
      where: { id: productId },
      data: {
        avgRating: Math.round(avgRating * 10) / 10, // Round to 1 decimal place
        reviewCount: totalReviews,
      },
    });
  } catch (error) {
    console.error("Error updating product rating:", error);
  }
}
