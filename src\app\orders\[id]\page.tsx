"use client";

import { useParams } from "next/navigation";
import { useSession } from "next-auth/react";
import { useOrder } from "@/hooks/use-orders";
import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON>, Footer } from "@/components/layout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  CheckCircle,
  Package,
  Truck,
  MapPin,
  CreditCard,
  Calendar,
  ArrowLeft,
  Download,
} from "lucide-react";
import { toast } from "@/lib/toast";

interface Order {
  id: string;
  total: number;
  status: string;
  paymentMethod: string;
  paymentStatus: string;
  shippingAddress: any;
  billingAddress: any;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  items: Array<{
    id: string;
    quantity: number;
    price: number;
    product: {
      id: string;
      name: string;
      images: string[];
      slug: string;
    };
  }>;
}

export default function OrderDetailPage() {
  const params = useParams();
  const { data: session } = useSession();
  const { order, loading, error } = useOrder(params.id as string);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("vi-VN", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "PENDING":
        return "text-yellow-600 bg-yellow-100";
      case "CONFIRMED":
        return "text-blue-600 bg-blue-100";
      case "PROCESSING":
        return "text-purple-600 bg-purple-100";
      case "SHIPPED":
        return "text-orange-600 bg-orange-100";
      case "DELIVERED":
        return "text-green-600 bg-green-100";
      case "CANCELLED":
        return "text-red-600 bg-red-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "PENDING":
        return "Chờ xác nhận";
      case "CONFIRMED":
        return "Đã xác nhận";
      case "PROCESSING":
        return "Đang xử lý";
      case "SHIPPED":
        return "Đang giao hàng";
      case "DELIVERED":
        return "Đã giao hàng";
      case "CANCELLED":
        return "Đã hủy";
      default:
        return status;
    }
  };

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case "COD":
        return "Thanh toán khi nhận hàng";
      case "BANK_TRANSFER":
        return "Chuyển khoản ngân hàng";
      case "CREDIT_CARD":
        return "Thẻ tín dụng";
      default:
        return method;
    }
  };

  if (!session) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-muted-foreground">
                Vui lòng đăng nhập để xem chi tiết đơn hàng
              </p>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-8" />
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2 space-y-6">
                <Card>
                  <CardContent className="p-6 space-y-4">
                    <div className="h-4 bg-gray-200 rounded" />
                    <div className="h-4 bg-gray-200 rounded w-2/3" />
                    <div className="h-20 bg-gray-200 rounded" />
                  </CardContent>
                </Card>
              </div>
              <div>
                <Card>
                  <CardContent className="p-6 space-y-4">
                    <div className="h-4 bg-gray-200 rounded" />
                    <div className="h-4 bg-gray-200 rounded" />
                    <div className="h-4 bg-gray-200 rounded" />
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (!order) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <Package className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h1 className="text-2xl font-bold mb-4">Không tìm thấy đơn hàng</h1>
            <Link href="/orders">
              <Button>Xem danh sách đơn hàng</Button>
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Link
            href="/orders"
            className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            Quay lại danh sách đơn hàng
          </Link>
        </div>

        {/* Success Message */}
        {order.status === "PENDING" && (
          <Card className="mb-8 border-green-200 bg-green-50">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <CheckCircle className="h-12 w-12 text-green-600" />
                <div>
                  <h2 className="text-xl font-bold text-green-800 mb-1">
                    Đặt hàng thành công!
                  </h2>
                  <p className="text-green-700">
                    Cảm ơn bạn đã đặt hàng. Chúng tôi sẽ xử lý đơn hàng của bạn
                    trong thời gian sớm nhất.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Order Info */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>
                    Đơn hàng #{order.id.slice(-8).toUpperCase()}
                  </CardTitle>
                  <span
                    className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(
                      order.status
                    )}`}
                  >
                    {getStatusText(order.status)}
                  </span>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>Ngày đặt: {formatDate(order.createdAt)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4 text-muted-foreground" />
                    <span>{getPaymentMethodText(order.paymentMethod)}</span>
                  </div>
                </div>

                {order.notes && (
                  <div>
                    <h4 className="font-medium mb-2">Ghi chú đơn hàng</h4>
                    <p className="text-sm text-muted-foreground bg-gray-50 p-3 rounded">
                      {order.notes}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Order Items */}
            <Card>
              <CardHeader>
                <CardTitle>Sản phẩm đã đặt ({order.items.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {order.items.map((item) => (
                    <div
                      key={item.id}
                      className="flex gap-4 pb-4 border-b last:border-b-0"
                    >
                      <Link href={`/products/${item.product.slug}`}>
                        <div className="relative w-16 h-16 flex-shrink-0 overflow-hidden rounded-lg bg-gray-100">
                          <Image
                            src={
                              item.product.images[0] ||
                              "/images/placeholder.jpg"
                            }
                            alt={item.product.name}
                            fill
                            className="object-cover hover:scale-105 transition-transform duration-200"
                          />
                        </div>
                      </Link>
                      <div className="flex-1">
                        <Link href={`/products/${item.product.slug}`}>
                          <h4 className="font-medium hover:text-pink-600 transition-colors">
                            {item.product.name}
                          </h4>
                        </Link>
                        <p className="text-sm text-muted-foreground">
                          Số lượng: {item.quantity}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Đơn giá: {formatPrice(item.price)}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">
                          {formatPrice(item.price * item.quantity)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Shipping & Billing Address */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Truck className="h-5 w-5" />
                    Địa chỉ giao hàng
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-1 text-sm">
                    <p className="font-medium">
                      {order.shippingAddress.fullName}
                    </p>
                    <p>{order.shippingAddress.phone}</p>
                    <p>{order.shippingAddress.address}</p>
                    <p>
                      {order.shippingAddress.ward},{" "}
                      {order.shippingAddress.district}
                    </p>
                    <p>{order.shippingAddress.province}</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="h-5 w-5" />
                    Địa chỉ thanh toán
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-1 text-sm">
                    <p className="font-medium">
                      {order.billingAddress.fullName}
                    </p>
                    <p>{order.billingAddress.phone}</p>
                    <p>{order.billingAddress.address}</p>
                    <p>
                      {order.billingAddress.ward},{" "}
                      {order.billingAddress.district}
                    </p>
                    <p>{order.billingAddress.province}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Order Summary */}
          <div>
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle>Tóm tắt đơn hàng</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Tạm tính</span>
                    <span>{formatPrice(order.total)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Phí vận chuyển</span>
                    <span className="text-green-600">Miễn phí</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-semibold">
                      <span>Tổng cộng</span>
                      <span className="text-pink-600">
                        {formatPrice(order.total)}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => {
                      // Download invoice logic
                      toast.success("Tính năng tải hóa đơn sẽ sớm có");
                    }}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Tải hóa đơn
                  </Button>

                  {order.status === "PENDING" && (
                    <Button
                      variant="outline"
                      className="w-full text-red-600 border-red-600 hover:bg-red-50"
                      onClick={() => {
                        // Cancel order logic
                        toast.success("Tính năng hủy đơn hàng sẽ sớm có");
                      }}
                    >
                      Hủy đơn hàng
                    </Button>
                  )}

                  <Link href="/products">
                    <Button className="w-full bg-pink-600 hover:bg-pink-700">
                      Tiếp tục mua sắm
                    </Button>
                  </Link>
                </div>

                {/* Order Status Timeline */}
                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-3">Trạng thái đơn hàng</h4>
                  <div className="space-y-3">
                    {[
                      {
                        status: "PENDING",
                        label: "Chờ xác nhận",
                        icon: Package,
                      },
                      {
                        status: "CONFIRMED",
                        label: "Đã xác nhận",
                        icon: CheckCircle,
                      },
                      {
                        status: "PROCESSING",
                        label: "Đang xử lý",
                        icon: Package,
                      },
                      {
                        status: "SHIPPED",
                        label: "Đang giao hàng",
                        icon: Truck,
                      },
                      {
                        status: "DELIVERED",
                        label: "Đã giao hàng",
                        icon: CheckCircle,
                      },
                    ].map(({ status, label, icon: Icon }) => {
                      const isActive = order.status === status;
                      const isPassed =
                        [
                          "PENDING",
                          "CONFIRMED",
                          "PROCESSING",
                          "SHIPPED",
                          "DELIVERED",
                        ].indexOf(order.status) >=
                        [
                          "PENDING",
                          "CONFIRMED",
                          "PROCESSING",
                          "SHIPPED",
                          "DELIVERED",
                        ].indexOf(status);

                      return (
                        <div key={status} className="flex items-center gap-3">
                          <div
                            className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                              isPassed
                                ? "bg-pink-600 border-pink-600 text-white"
                                : "border-gray-300 text-gray-400"
                            }`}
                          >
                            <Icon className="h-4 w-4" />
                          </div>
                          <span
                            className={`text-sm ${
                              isActive
                                ? "font-medium text-pink-600"
                                : isPassed
                                  ? "text-foreground"
                                  : "text-muted-foreground"
                            }`}
                          >
                            {label}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
