import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { FeaturedProducts } from '../featured-products';
import { useFeaturedProducts } from '@/hooks/use-products';
import { useEnhancedCart } from '@/contexts/enhanced-cart-context';
import { useUserContext } from '@/contexts/user-context';

// Mock the hooks
jest.mock('@/hooks/use-products');
jest.mock('@/contexts/enhanced-cart-context');
jest.mock('@/contexts/user-context');

const mockUseFeaturedProducts = useFeaturedProducts as jest.MockedFunction<typeof useFeaturedProducts>;
const mockUseEnhancedCart = useEnhancedCart as jest.MockedFunction<typeof useEnhancedCart>;
const mockUseUserContext = useUserContext as jest.MockedFunction<typeof useUserContext>;

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
  }),
}));

// Mock data
const mockProducts = [
  {
    id: '1',
    name: 'Áo Thun Nam Basic',
    slug: 'ao-thun-nam-basic',
    price: 299000,
    salePrice: 199000,
    description: 'Áo thun nam chất liệu cotton cao cấp',
    images: ['https://example.com/image1.jpg'],
    stock: 10,
    category: { id: '1', name: 'Áo Nam', slug: 'ao-nam' },
    attributes: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: '2',
    name: 'Quần Jeans Nữ Skinny',
    slug: 'quan-jeans-nu-skinny',
    price: 599000,
    salePrice: null,
    description: 'Quần jeans nữ form skinny thời trang',
    images: ['https://example.com/image2.jpg'],
    stock: 5,
    category: { id: '2', name: 'Quần Nữ', slug: 'quan-nu' },
    attributes: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

const mockCartContext = {
  cart: null,
  loading: false,
  error: null,
  summary: null,
  addToCart: jest.fn(),
  updateCartItem: jest.fn(),
  removeFromCart: jest.fn(),
  clearCart: jest.fn(),
  refetch: jest.fn(),
  quickAdd: jest.fn(),
  quickAddLoading: false,
  validationErrors: [],
  isValid: true,
  getItemQuantity: jest.fn().mockReturnValue(0),
  isInCart: jest.fn().mockReturnValue(false),
  canAddToCart: jest.fn().mockReturnValue(true),
};

const mockUserContext = {
  user: null,
  loading: false,
  error: null,
  updateProfile: jest.fn(),
  addAddress: jest.fn(),
  updateAddress: jest.fn(),
  deleteAddress: jest.fn(),
  setDefaultAddress: jest.fn(),
  addToWishlist: jest.fn(),
  removeFromWishlist: jest.fn(),
  wishlist: [],
  wishlistLoading: false,
  addresses: [],
  addressesLoading: false,
  profileCompletionPercentage: 0,
  refetch: jest.fn(),
};

describe('FeaturedProducts', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseEnhancedCart.mockReturnValue(mockCartContext);
    mockUseUserContext.mockReturnValue(mockUserContext);
  });

  it('should render loading state', () => {
    mockUseFeaturedProducts.mockReturnValue({
      products: [],
      loading: true,
      error: null,
      refetch: jest.fn(),
    });

    render(<FeaturedProducts />);

    expect(screen.getByText('Đang tải...')).toBeInTheDocument();
  });

  it('should render error state', () => {
    mockUseFeaturedProducts.mockReturnValue({
      products: [],
      loading: false,
      error: 'Failed to load products',
      refetch: jest.fn(),
    });

    render(<FeaturedProducts />);

    expect(screen.getByText('Có lỗi xảy ra khi tải sản phẩm')).toBeInTheDocument();
  });

  it('should render empty state', () => {
    mockUseFeaturedProducts.mockReturnValue({
      products: [],
      loading: false,
      error: null,
      refetch: jest.fn(),
    });

    render(<FeaturedProducts />);

    expect(screen.getByText('Không có sản phẩm nổi bật')).toBeInTheDocument();
  });

  it('should render featured products', () => {
    mockUseFeaturedProducts.mockReturnValue({
      products: mockProducts,
      loading: false,
      error: null,
      refetch: jest.fn(),
    });

    render(<FeaturedProducts />);

    expect(screen.getByText('Sản Phẩm Nổi Bật')).toBeInTheDocument();
    expect(screen.getByText('Áo Thun Nam Basic')).toBeInTheDocument();
    expect(screen.getByText('Quần Jeans Nữ Skinny')).toBeInTheDocument();
  });

  it('should display product prices correctly', () => {
    mockUseFeaturedProducts.mockReturnValue({
      products: mockProducts,
      loading: false,
      error: null,
      refetch: jest.fn(),
    });

    render(<FeaturedProducts />);

    // Product with sale price
    expect(screen.getByText('199.000₫')).toBeInTheDocument();
    expect(screen.getByText('299.000₫')).toBeInTheDocument();

    // Product without sale price
    expect(screen.getByText('599.000₫')).toBeInTheDocument();
  });

  it('should handle add to cart', async () => {
    mockUseFeaturedProducts.mockReturnValue({
      products: mockProducts,
      loading: false,
      error: null,
      refetch: jest.fn(),
    });

    render(<FeaturedProducts />);

    const addToCartButtons = screen.getAllByText('Thêm vào giỏ');
    fireEvent.click(addToCartButtons[0]);

    await waitFor(() => {
      expect(mockCartContext.quickAdd).toHaveBeenCalledWith('1', 1);
    });
  });

  it('should handle add to wishlist', async () => {
    mockUseFeaturedProducts.mockReturnValue({
      products: mockProducts,
      loading: false,
      error: null,
      refetch: jest.fn(),
    });

    render(<FeaturedProducts />);

    const wishlistButtons = screen.getAllByRole('button', { name: /wishlist/i });
    fireEvent.click(wishlistButtons[0]);

    await waitFor(() => {
      expect(mockUserContext.addToWishlist).toHaveBeenCalledWith('1');
    });
  });

  it('should show different wishlist state for items in wishlist', () => {
    const wishlistWithItem = [
      {
        id: 'wishlist-1',
        productId: '1',
        product: mockProducts[0],
        createdAt: new Date().toISOString(),
      },
    ];

    mockUseUserContext.mockReturnValue({
      ...mockUserContext,
      wishlist: wishlistWithItem,
    });

    mockUseFeaturedProducts.mockReturnValue({
      products: mockProducts,
      loading: false,
      error: null,
      refetch: jest.fn(),
    });

    render(<FeaturedProducts />);

    // First product should show as in wishlist
    const wishlistButtons = screen.getAllByRole('button', { name: /wishlist/i });
    expect(wishlistButtons[0]).toHaveClass('text-pink-600'); // Assuming this class indicates item is in wishlist
  });

  it('should handle out of stock products', () => {
    const outOfStockProducts = [
      { ...mockProducts[0], stock: 0 },
      mockProducts[1],
    ];

    mockUseFeaturedProducts.mockReturnValue({
      products: outOfStockProducts,
      loading: false,
      error: null,
      refetch: jest.fn(),
    });

    render(<FeaturedProducts />);

    expect(screen.getByText('Hết hàng')).toBeInTheDocument();
  });

  it('should show loading state for quick add', () => {
    mockUseEnhancedCart.mockReturnValue({
      ...mockCartContext,
      quickAddLoading: true,
    });

    mockUseFeaturedProducts.mockReturnValue({
      products: mockProducts,
      loading: false,
      error: null,
      refetch: jest.fn(),
    });

    render(<FeaturedProducts />);

    // Should show loading indicator on add to cart button
    expect(screen.getByText('Đang thêm...')).toBeInTheDocument();
  });

  it('should handle product click navigation', () => {
    mockUseFeaturedProducts.mockReturnValue({
      products: mockProducts,
      loading: false,
      error: null,
      refetch: jest.fn(),
    });

    render(<FeaturedProducts />);

    const productLinks = screen.getAllByRole('link');
    expect(productLinks[0]).toHaveAttribute('href', '/products/ao-thun-nam-basic');
    expect(productLinks[1]).toHaveAttribute('href', '/products/quan-jeans-nu-skinny');
  });

  it('should display discount percentage for sale items', () => {
    mockUseFeaturedProducts.mockReturnValue({
      products: mockProducts,
      loading: false,
      error: null,
      refetch: jest.fn(),
    });

    render(<FeaturedProducts />);

    // Calculate discount: (299000 - 199000) / 299000 * 100 = 33%
    expect(screen.getByText('-33%')).toBeInTheDocument();
  });

  it('should handle retry on error', async () => {
    const mockRefetch = jest.fn();
    mockUseFeaturedProducts.mockReturnValue({
      products: [],
      loading: false,
      error: 'Network error',
      refetch: mockRefetch,
    });

    render(<FeaturedProducts />);

    const retryButton = screen.getByText('Thử lại');
    fireEvent.click(retryButton);

    expect(mockRefetch).toHaveBeenCalled();
  });
});
