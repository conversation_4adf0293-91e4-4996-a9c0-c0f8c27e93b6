import { test, expect } from '@playwright/test';

test.describe('Responsive Design Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('should display correctly on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);
    
    // Check if navigation is responsive
    const nav = page.locator('nav');
    await expect(nav).toBeVisible();
    
    // Check if mobile menu button exists
    const mobileMenuButton = page.locator('button[aria-label*="menu"], button:has(svg), .mobile-menu-button, [data-testid*="mobile-menu"]');
    const mobileMenuCount = await mobileMenuButton.count();
    
    if (mobileMenuCount > 0) {
      console.log('Mobile menu button found');
      
      // Test mobile menu toggle
      await mobileMenuButton.first().click();
      await page.waitForTimeout(1000);
      
      // Check if menu opened
      const mobileMenu = page.locator('.mobile-menu, [data-testid*="mobile-menu"], nav ul');
      if (await mobileMenu.isVisible().catch(() => false)) {
        console.log('Mobile menu opened successfully');
      }
    } else {
      console.log('No mobile menu button found');
    }
    
    // Check if hero section is responsive
    const heroSection = page.locator('main').first();
    await expect(heroSection).toBeVisible();
    
    // Check if text is readable on mobile
    const heroTitle = page.locator('h1');
    await expect(heroTitle).toBeVisible();
    
    // Check if buttons are accessible on mobile
    const heroButtons = page.locator('a[href="/products"], a[href="/categories"]');
    await expect(heroButtons.first()).toBeVisible();
  });

  test('should display correctly on tablet viewport', async ({ page }) => {
    // Set tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(1000);
    
    // Check navigation
    const nav = page.locator('nav');
    await expect(nav).toBeVisible();
    
    // Check if navigation items are visible
    const navItems = page.locator('nav a');
    const navCount = await navItems.count();
    
    if (navCount > 0) {
      console.log(`Found ${navCount} navigation items on tablet`);
    }
    
    // Check product grid layout
    const productCards = page.locator('a[href^="/products/"]');
    const productCount = await productCards.count();
    
    if (productCount > 0) {
      console.log(`Found ${productCount} product cards on tablet`);
    }
    
    // Check categories grid
    const categoryCards = page.locator('a[href^="/categories/"]');
    const categoryCount = await categoryCards.count();
    
    if (categoryCount > 0) {
      console.log(`Found ${categoryCount} category cards on tablet`);
    }
  });

  test('should display correctly on desktop viewport', async ({ page }) => {
    // Set desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.waitForTimeout(1000);
    
    // Check navigation
    const nav = page.locator('nav');
    await expect(nav).toBeVisible();
    
    // Check if all navigation items are visible
    const expectedNavItems = ['Trang chủ', 'Sản phẩm', 'Danh mục', 'Về chúng tôi', 'Liên hệ'];
    
    for (const item of expectedNavItems) {
      const navItem = page.locator(`nav a:has-text("${item}")`);
      await expect(navItem).toBeVisible();
    }
    
    // Check if search bar is visible
    const searchInput = page.locator('input[placeholder*="Tìm kiếm"]');
    await expect(searchInput).toBeVisible();
    
    // Check if cart and auth buttons are visible
    const cartButton = page.locator('button:has-text("0")');
    await expect(cartButton).toBeVisible();
    
    const authLinks = page.locator('a[href="/auth/signin"], a[href="/auth/signup"]');
    await expect(authLinks.first()).toBeVisible();
  });

  test('should test mobile menu functionality', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);
    
    // Look for mobile menu button
    const mobileMenuButtons = [
      page.locator('button[aria-label*="menu"]'),
      page.locator('button:has(svg[data-lucide="menu"])'),
      page.locator('.mobile-menu-button'),
      page.locator('[data-testid*="mobile-menu"]'),
      page.locator('button:has-text("☰")'),
      page.locator('button:has-text("Menu")')
    ];
    
    let mobileMenuButton = null;
    for (const buttonGroup of mobileMenuButtons) {
      if (await buttonGroup.first().isVisible().catch(() => false)) {
        mobileMenuButton = buttonGroup.first();
        break;
      }
    }
    
    if (mobileMenuButton) {
      console.log('Mobile menu button found');
      
      // Test opening menu
      await mobileMenuButton.click();
      await page.waitForTimeout(1000);
      
      // Check if navigation items are now visible
      const mobileNavItems = page.locator('nav a, .mobile-menu a');
      const visibleItems = await mobileNavItems.count();
      
      if (visibleItems > 0) {
        console.log(`Mobile menu opened with ${visibleItems} items`);
        
        // Test clicking a menu item
        const firstNavItem = mobileNavItems.first();
        if (await firstNavItem.isVisible()) {
          // Don't actually navigate, just test the click
          console.log('Mobile menu item is clickable');
        }
      }
      
      // Test closing menu (click button again)
      await mobileMenuButton.click();
      await page.waitForTimeout(1000);
      console.log('Mobile menu toggled again');
    } else {
      console.log('No mobile menu button found');
    }
  });

  test('should test responsive images', async ({ page }) => {
    const viewports = [
      { width: 375, height: 667, name: 'mobile' },
      { width: 768, height: 1024, name: 'tablet' },
      { width: 1920, height: 1080, name: 'desktop' }
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.waitForTimeout(1000);
      
      // Check hero image
      const heroImages = page.locator('main img, .hero img');
      const heroImageCount = await heroImages.count();
      
      if (heroImageCount > 0) {
        console.log(`Hero images visible on ${viewport.name}: ${heroImageCount}`);
      }
      
      // Check product images
      const productImages = page.locator('a[href^="/products/"] img');
      const productImageCount = await productImages.count();
      
      if (productImageCount > 0) {
        console.log(`Product images visible on ${viewport.name}: ${productImageCount}`);
      }
      
      // Check category images
      const categoryImages = page.locator('a[href^="/categories/"] img');
      const categoryImageCount = await categoryImages.count();
      
      if (categoryImageCount > 0) {
        console.log(`Category images visible on ${viewport.name}: ${categoryImageCount}`);
      }
    }
  });

  test('should test responsive text and buttons', async ({ page }) => {
    const viewports = [
      { width: 375, height: 667, name: 'mobile' },
      { width: 768, height: 1024, name: 'tablet' },
      { width: 1920, height: 1080, name: 'desktop' }
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.waitForTimeout(1000);
      
      // Check hero title
      const heroTitle = page.locator('h1');
      await expect(heroTitle).toBeVisible();
      
      // Check hero buttons
      const heroButtons = page.locator('a[href="/products"], a[href="/categories"]');
      await expect(heroButtons.first()).toBeVisible();
      
      // Check add to cart buttons
      const addToCartButtons = page.locator('button:has-text("Thêm vào giỏ")');
      const buttonCount = await addToCartButtons.count();
      
      if (buttonCount > 0) {
        console.log(`Add to cart buttons visible on ${viewport.name}: ${buttonCount}`);
      }
      
      console.log(`Layout tested on ${viewport.name} (${viewport.width}x${viewport.height})`);
    }
  });

  test('should test footer responsiveness', async ({ page }) => {
    const viewports = [
      { width: 375, height: 667, name: 'mobile' },
      { width: 768, height: 1024, name: 'tablet' },
      { width: 1920, height: 1080, name: 'desktop' }
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.waitForTimeout(1000);
      
      // Scroll to footer
      await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
      await page.waitForTimeout(1000);
      
      // Check footer visibility
      const footer = page.locator('contentinfo');
      if (await footer.isVisible().catch(() => false)) {
        console.log(`Footer visible on ${viewport.name}`);
        
        // Check footer links
        const footerLinks = footer.locator('a');
        const linkCount = await footerLinks.count();
        
        if (linkCount > 0) {
          console.log(`Footer links on ${viewport.name}: ${linkCount}`);
        }
      } else {
        console.log(`Footer not visible on ${viewport.name}`);
      }
    }
  });

  test('should test touch interactions on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);
    
    // Test touch on product cards
    const productCards = page.locator('a[href^="/products/"]');
    const productCount = await productCards.count();
    
    if (productCount > 0) {
      // Test tap on first product card
      await productCards.first().tap();
      await page.waitForTimeout(1000);
      console.log('Product card tap tested');
      
      // Go back to home
      await page.goBack();
      await page.waitForTimeout(1000);
    }
    
    // Test touch on category cards
    const categoryCards = page.locator('a[href^="/categories/"]');
    const categoryCount = await categoryCards.count();
    
    if (categoryCount > 0) {
      // Test tap on first category card
      await categoryCards.first().tap();
      await page.waitForTimeout(1000);
      console.log('Category card tap tested');
      
      // Go back to home
      await page.goBack();
      await page.waitForTimeout(1000);
    }
    
    // Test touch on buttons
    const buttons = page.locator('button:has-text("Thêm vào giỏ")');
    const buttonCount = await buttons.count();
    
    if (buttonCount > 0) {
      await buttons.first().tap();
      await page.waitForTimeout(1000);
      console.log('Add to cart button tap tested');
    }
  });

  test('should test horizontal scrolling if present', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);
    
    // Look for horizontally scrollable elements
    const scrollableElements = [
      page.locator('.overflow-x-auto'),
      page.locator('.scroll-x'),
      page.locator('[style*="overflow-x"]'),
      page.locator('.horizontal-scroll')
    ];
    
    let hasHorizontalScroll = false;
    for (const elementGroup of scrollableElements) {
      const count = await elementGroup.count();
      if (count > 0) {
        hasHorizontalScroll = true;
        console.log(`Found ${count} horizontally scrollable elements`);
        
        // Test scrolling
        const firstElement = elementGroup.first();
        await firstElement.evaluate(el => el.scrollLeft = 100);
        await page.waitForTimeout(1000);
        console.log('Horizontal scroll tested');
        break;
      }
    }
    
    if (!hasHorizontalScroll) {
      console.log('No horizontal scroll elements found');
    }
  });
});
