"use client";

import Link from "next/link";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { useSession, signOut } from "next-auth/react";
import {
  Search,
  ShoppingBag,
  User,
  Menu,
  X,
  Heart,
  LogOut,
  Settings,
  Package,
  Phone,
  Mail,
  MapPin,
  Gift,
  Zap,
  Clock,
  ChevronDown,
  Star,
  Truck,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useSettingsContext } from "@/contexts/SettingsContext";
import { useEnhancedCart } from "@/contexts/enhanced-cart-context";
import { useSearchContext } from "@/contexts/search-context";
import { useRootCategories } from "@/hooks/use-categories";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [showTopBanner, setShowTopBanner] = useState(true);
  const { settings } = useSettingsContext();
  const { summary } = useEnhancedCart();
  const totalItems = summary?.totalQuantity || 0;
  const { setQuery } = useSearchContext();
  const { categories: rootCategories, loading: categoriesLoading } =
    useRootCategories();
  const router = useRouter();
  const { data: session } = useSession();

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      setQuery(searchQuery.trim());
      router.push(`/products?search=${encodeURIComponent(searchQuery.trim())}`);
      setSearchQuery("");
    }
  };

  return (
    <header className="sticky top-0 z-50 w-full">
      {/* Top Announcement Banner */}
      {showTopBanner && (
        <div className="bg-gradient-to-r from-fashion-600 via-fashion-500 to-fashion-600 text-sm bg-white/95 text-gray-600">
          <div className="container mx-auto px-4">
            <div className="flex items-center justify-between py-2">
              <div className="flex items-center gap-4 flex-1">
                <div className="flex items-center gap-2">
                  <Gift className="h-4 w-4" />
                  <span className="font-medium">MIỄN PHÍ VẬN CHUYỂN</span>
                  <span>
                    cho đơn hàng từ{" "}
                    {settings.shippingSettings.freeShippingThreshold.toLocaleString(
                      "vi-VN"
                    )}
                    đ
                  </span>
                </div>
                <div className="hidden md:flex items-center gap-2">
                  <Truck className="h-4 w-4" />
                  <span>
                    Giao hàng {settings.shippingSettings.estimatedDelivery}
                  </span>
                </div>
                <div className="hidden lg:flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  <span>Hotline: {settings.contactInfo.phone}</span>
                </div>
              </div>
              <button
                onClick={() => setShowTopBanner(false)}
                className="text-white/80 hover:text-white transition-colors"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Main Header */}
      <div className="bg-white/95 backdrop-blur-md border-b border-gray-100 shadow-sm">
        <div className="container mx-auto px-4">
          {/* Top Row - Contact Info & Quick Links */}
          <div className="hidden lg:flex items-center justify-between py-2 text-sm border-b border-gray-100">
            <div className="flex items-center gap-6 text-gray-600">
              <div className="flex items-center gap-2">
                <MapPin className="h-3 w-3" />
                <span>{settings.contactInfo.address}</span>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="h-3 w-3" />
                <span>{settings.contactInfo.email}</span>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1">
                <Star className="h-3 w-3 text-yellow-500 fill-current" />
                <span className="text-xs font-medium">
                  4.9/5 từ 10k+ đánh giá
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-3 w-3" />
                <span className="text-xs">Hỗ trợ 24/7</span>
              </div>
            </div>
          </div>

          {/* Main Navigation Row */}
          <div className="flex h-20 items-center justify-between">
            {/* Enhanced Logo */}
            <Link href="/" className="flex items-center space-x-3 group">
              <div className="relative">
                <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-fashion-500 via-fashion-600 to-fashion-700 shadow-lg group-hover:shadow-xl transition-all duration-300 flex items-center justify-center">
                  <span className="text-white font-bold text-lg">NS</span>
                </div>
                <div className="absolute -top-1 -right-1 h-4 w-4 bg-yellow-400 rounded-full flex items-center justify-center">
                  <Zap className="h-2 w-2 text-yellow-800" />
                </div>
              </div>
              <div className="flex flex-col">
                <span className="text-2xl font-bold bg-gradient-to-r from-fashion-600 to-fashion-800 bg-clip-text text-transparent">
                  {settings.siteInfo.name || "NS Shop"}
                </span>
                <span className="text-xs text-gray-500 font-medium">
                  Fashion Store
                </span>
              </div>
            </Link>

            {/* Enhanced Desktop Navigation */}
            <nav className="hidden lg:flex items-center">
              <div className="flex items-center bg-gray-50 rounded-full p-1">
                <Link
                  href="/"
                  className="px-6 py-2 text-sm font-medium rounded-full transition-all duration-200 hover:bg-white hover:shadow-sm text-gray-700 hover:text-fashion-600"
                >
                  Trang chủ
                </Link>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button className="flex items-center gap-1 px-6 py-2 text-sm font-medium rounded-full transition-all duration-200 hover:bg-white hover:shadow-sm text-gray-700 hover:text-fashion-600">
                      Sản phẩm
                      <ChevronDown className="h-3 w-3" />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56">
                    <DropdownMenuItem asChild>
                      <Link href="/products">Tất cả sản phẩm</Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    {!categoriesLoading &&
                      rootCategories &&
                      rootCategories.length > 0 && (
                        <>
                          {rootCategories.slice(0, 6).map((category) => (
                            <DropdownMenuItem key={category.id} asChild>
                              <Link href={`/categories/${category.slug}`}>
                                {category.name}
                              </Link>
                            </DropdownMenuItem>
                          ))}
                          <DropdownMenuSeparator />
                        </>
                      )}
                    <DropdownMenuItem asChild>
                      <Link href="/products?filter=new">Sản phẩm mới</Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/products?filter=sale">Khuyến mãi</Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/products?filter=trending">Trending</Link>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                <Link
                  href="/categories"
                  className="px-6 py-2 text-sm font-medium rounded-full transition-all duration-200 hover:bg-white hover:shadow-sm text-gray-700 hover:text-fashion-600"
                >
                  Danh mục
                </Link>
                <Link
                  href="/about"
                  className="px-6 py-2 text-sm font-medium rounded-full transition-all duration-200 hover:bg-white hover:shadow-sm text-gray-700 hover:text-fashion-600"
                >
                  Về chúng tôi
                </Link>
                <Link
                  href="/contact"
                  className="px-6 py-2 text-sm font-medium rounded-full transition-all duration-200 hover:bg-white hover:shadow-sm text-gray-700 hover:text-fashion-600"
                >
                  Liên hệ
                </Link>
              </div>
            </nav>

            {/* Enhanced Search Bar */}
            <div className="hidden lg:flex items-center flex-1 max-w-2xl mx-8">
              <form onSubmit={handleSearch} className="relative w-full group">
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 group-focus-within:text-fashion-500 transition-colors" />
                  <input
                    type="text"
                    placeholder="Tìm kiếm sản phẩm, thương hiệu, danh mục..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-12 pr-32 py-4 border-2 border-gray-200 rounded-2xl bg-gray-50 text-sm focus:outline-none focus:ring-0 focus:border-fashion-500 focus:bg-white transition-all duration-200 placeholder:text-gray-400"
                  />
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
                    <Badge variant="secondary" className="text-xs px-2 py-1">
                      Ctrl+K
                    </Badge>
                    <Button
                      type="submit"
                      size="sm"
                      className="bg-fashion-600 hover:bg-fashion-700 text-white rounded-xl px-4"
                    >
                      Tìm kiếm
                    </Button>
                  </div>
                </div>
              </form>
            </div>

            {/* Enhanced Actions */}
            <div className="flex items-center space-x-3">
              {/* Mobile Search */}
              <Button
                variant="ghost"
                size="icon"
                className="lg:hidden relative group"
                onClick={() => setIsMenuOpen(true)}
              >
                <Search className="h-5 w-5 text-gray-600 group-hover:text-fashion-600 transition-colors" />
              </Button>

              {/* Wishlist */}
              <Link href="/wishlist" className="hidden sm:block">
                <Button variant="ghost" size="icon" className="relative group">
                  <Heart className="h-5 w-5 text-gray-600 group-hover:text-red-500 transition-colors" />
                  <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 text-xs text-white flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                    3
                  </span>
                </Button>
              </Link>

              {/* Enhanced Cart */}
              <Link href="/cart">
                <Button variant="ghost" size="icon" className="relative group">
                  <div className="relative">
                    <ShoppingBag className="h-5 w-5 text-gray-600 group-hover:text-fashion-600 transition-colors" />
                    {totalItems > 0 && (
                      <span className="absolute -top-2 -right-2 h-5 w-5 rounded-full bg-fashion-600 text-xs text-white flex items-center justify-center font-medium shadow-lg">
                        {totalItems}
                      </span>
                    )}
                  </div>
                </Button>
              </Link>

              {/* Enhanced User Menu */}
              <div className="hidden sm:flex">
                {session ? (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        className="flex items-center gap-2 px-3 py-2 rounded-xl hover:bg-gray-100 transition-colors"
                      >
                        <div className="h-8 w-8 rounded-full bg-gradient-to-br from-fashion-500 to-fashion-600 flex items-center justify-center">
                          <span className="text-white font-medium text-sm">
                            {session.user?.name?.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div className="hidden md:flex flex-col items-start">
                          <span className="text-sm font-medium text-gray-900">
                            {session.user?.name}
                          </span>
                          <span className="text-xs text-gray-500">
                            Thành viên
                          </span>
                        </div>
                        <ChevronDown className="h-3 w-3 text-gray-400" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-64 p-2">
                      <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-fashion-50 to-fashion-100 rounded-lg mb-2">
                        <div className="h-10 w-10 rounded-full bg-gradient-to-br from-fashion-500 to-fashion-600 flex items-center justify-center">
                          <span className="text-white font-medium">
                            {session.user?.name?.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">
                            {session.user?.name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {session.user?.email}
                          </p>
                        </div>
                      </div>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link
                          href="/profile"
                          className="cursor-pointer flex items-center gap-3 p-2 rounded-lg"
                        >
                          <User className="h-4 w-4 text-gray-500" />
                          <span>Thông tin cá nhân</span>
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link
                          href="/orders"
                          className="cursor-pointer flex items-center gap-3 p-2 rounded-lg"
                        >
                          <Package className="h-4 w-4 text-gray-500" />
                          <span>Đơn hàng của tôi</span>
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link
                          href="/wishlist"
                          className="cursor-pointer flex items-center gap-3 p-2 rounded-lg"
                        >
                          <Heart className="h-4 w-4 text-gray-500" />
                          <span>Danh sách yêu thích</span>
                        </Link>
                      </DropdownMenuItem>
                      {session.user?.role === "ADMIN" && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem asChild>
                            <Link
                              href="/admin"
                              className="cursor-pointer flex items-center gap-3 p-2 rounded-lg"
                            >
                              <Settings className="h-4 w-4 text-gray-500" />
                              <span>Quản trị</span>
                            </Link>
                          </DropdownMenuItem>
                        </>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        className="cursor-pointer text-red-600 focus:text-red-600 flex items-center gap-3 p-2 rounded-lg"
                        onClick={() => signOut()}
                      >
                        <LogOut className="h-4 w-4" />
                        <span>Đăng xuất</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : (
                  <div className="flex items-center space-x-3">
                    <Button
                      variant="ghost"
                      className="text-gray-700 hover:text-fashion-600 font-medium"
                      asChild
                    >
                      <Link href="/auth/signin">Đăng nhập</Link>
                    </Button>
                    <Button
                      className="bg-gradient-to-r from-fashion-600 to-fashion-700 hover:from-fashion-700 hover:to-fashion-800 text-white font-medium px-6 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                      asChild
                    >
                      <Link href="/auth/signup">Đăng ký</Link>
                    </Button>
                  </div>
                )}
              </div>

              {/* Enhanced Mobile Menu Toggle */}
              <Button
                variant="ghost"
                size="icon"
                className="lg:hidden relative group"
                onClick={toggleMenu}
              >
                <div className="relative">
                  {isMenuOpen ? (
                    <X className="h-5 w-5 text-gray-600 group-hover:text-fashion-600 transition-colors" />
                  ) : (
                    <Menu className="h-5 w-5 text-gray-600 group-hover:text-fashion-600 transition-colors" />
                  )}
                </div>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Mobile Menu */}
      {isMenuOpen && (
        <div className="lg:hidden bg-white border-t border-gray-100 shadow-lg">
          <div className="container mx-auto px-4 py-6">
            {/* Mobile Search */}
            <div className="mb-6">
              <form onSubmit={handleSearch} className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Tìm kiếm sản phẩm..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 border-2 border-gray-200 rounded-2xl bg-gray-50 text-sm focus:outline-none focus:border-fashion-500 focus:bg-white transition-all"
                />
              </form>
            </div>

            {/* Mobile Navigation */}
            <nav className="space-y-1 mb-6">
              <Link
                href="/"
                className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:text-fashion-600 hover:bg-fashion-50 rounded-xl transition-all font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Trang chủ
              </Link>
              <Link
                href="/products"
                className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:text-fashion-600 hover:bg-fashion-50 rounded-xl transition-all font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Sản phẩm
              </Link>

              {/* Dynamic Categories */}
              {!categoriesLoading &&
                rootCategories &&
                rootCategories.length > 0 && (
                  <div className="ml-4 space-y-1">
                    {rootCategories.slice(0, 4).map((category) => (
                      <Link
                        key={category.id}
                        href={`/categories/${category.slug}`}
                        className="flex items-center gap-3 px-4 py-2 text-sm text-gray-600 hover:text-fashion-600 hover:bg-fashion-50 rounded-lg transition-all"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {category.name}
                      </Link>
                    ))}
                  </div>
                )}

              <Link
                href="/categories"
                className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:text-fashion-600 hover:bg-fashion-50 rounded-xl transition-all font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Tất cả danh mục
              </Link>
              <Link
                href="/about"
                className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:text-fashion-600 hover:bg-fashion-50 rounded-xl transition-all font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Về chúng tôi
              </Link>
              <Link
                href="/contact"
                className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:text-fashion-600 hover:bg-fashion-50 rounded-xl transition-all font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Liên hệ
              </Link>
            </nav>

            {/* Mobile User Menu */}
            <div className="pt-4 border-t border-gray-100">
              {session ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-3 p-4 bg-gradient-to-r from-fashion-50 to-fashion-100 rounded-2xl">
                    <div className="h-12 w-12 rounded-full bg-gradient-to-br from-fashion-500 to-fashion-600 flex items-center justify-center">
                      <span className="text-white font-bold">
                        {session.user?.name?.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">
                        {session.user?.name}
                      </p>
                      <p className="text-sm text-gray-500">
                        {session.user?.email}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-1">
                    <Link
                      href="/profile"
                      className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:text-fashion-600 hover:bg-fashion-50 rounded-xl transition-all"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <User className="h-5 w-5" />
                      <span className="font-medium">Thông tin cá nhân</span>
                    </Link>
                    <Link
                      href="/orders"
                      className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:text-fashion-600 hover:bg-fashion-50 rounded-xl transition-all"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <Package className="h-5 w-5" />
                      <span className="font-medium">Đơn hàng của tôi</span>
                    </Link>
                    <Link
                      href="/wishlist"
                      className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:text-fashion-600 hover:bg-fashion-50 rounded-xl transition-all"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <Heart className="h-5 w-5" />
                      <span className="font-medium">Danh sách yêu thích</span>
                    </Link>
                    {session.user?.role === "ADMIN" && (
                      <Link
                        href="/admin"
                        className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:text-fashion-600 hover:bg-fashion-50 rounded-xl transition-all"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <Settings className="h-5 w-5" />
                        <span className="font-medium">Quản trị</span>
                      </Link>
                    )}
                    <button
                      onClick={() => {
                        signOut();
                        setIsMenuOpen(false);
                      }}
                      className="flex items-center gap-3 px-4 py-3 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-xl transition-all w-full text-left"
                    >
                      <LogOut className="h-5 w-5" />
                      <span className="font-medium">Đăng xuất</span>
                    </button>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  <Button
                    variant="outline"
                    className="w-full justify-center py-3 rounded-xl border-2 hover:border-fashion-500 hover:text-fashion-600"
                    asChild
                  >
                    <Link
                      href="/auth/signin"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Đăng nhập
                    </Link>
                  </Button>
                  <Button
                    className="w-full py-3 rounded-xl bg-gradient-to-r from-fashion-600 to-fashion-700 hover:from-fashion-700 hover:to-fashion-800 text-white font-medium shadow-lg"
                    asChild
                  >
                    <Link
                      href="/auth/signup"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Đăng ký ngay
                    </Link>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  );
}
