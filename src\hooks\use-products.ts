"use client";

import { useState, useEffect, useCallback } from "react";
import { Product } from "@/types";
import { ProductService, ProductListParams } from "@/lib/services";

// Hook state interface
interface UseProductsState {
  data: Product[];
  total: number;
  page: number;
  totalPages: number;
  loading: boolean;
  error: string | null;
}

// Main products hook with pagination and filtering
export function useProducts(params: ProductListParams = {}) {
  const [state, setState] = useState<UseProductsState>({
    data: [],
    total: 0,
    page: 1,
    totalPages: 0,
    loading: true,
    error: null,
  });

  const fetchProducts = useCallback(async () => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      const response = await ProductService.getProducts(params);

      setState({
        data: response.data,
        total: response.total,
        page: response.page,
        totalPages: response.totalPages,
        loading: false,
        error: null,
      });
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: false,
        error:
          error instanceof Error ? error.message : "Failed to fetch products",
      }));
    }
  }, [JSON.stringify(params)]);

  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  const refetch = useCallback(() => {
    fetchProducts();
  }, [fetchProducts]);

  return {
    ...state,
    refetch,
  };
}

// Single product hook
export function useProduct(id: string | null) {
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProduct = useCallback(async () => {
    if (!id) {
      setProduct(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await ProductService.getProduct(id);
      setProduct(response.data || null);
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to fetch product"
      );
      setProduct(null);
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchProduct();
  }, [fetchProduct]);

  const refetch = useCallback(() => {
    fetchProduct();
  }, [fetchProduct]);

  return {
    product,
    loading,
    error,
    refetch,
  };
}

// Product by slug hook
export function useProductBySlug(slug: string | null) {
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProduct = useCallback(async () => {
    if (!slug) {
      setProduct(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await ProductService.getProductBySlug(slug);
      setProduct(response.data || null);

      // Add to recently viewed
      if (response.data) {
        ProductService.addToRecentlyViewed(response.data);
      }
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to fetch product"
      );
      setProduct(null);
    } finally {
      setLoading(false);
    }
  }, [slug]);

  useEffect(() => {
    fetchProduct();
  }, [fetchProduct]);

  const refetch = useCallback(() => {
    fetchProduct();
  }, [fetchProduct]);

  return {
    product,
    loading,
    error,
    refetch,
  };
}

// Featured products hook
export function useFeaturedProducts(limit: number = 8) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchFeaturedProducts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await ProductService.getFeaturedProducts(limit);
      setProducts(response.data);
    } catch (error) {
      setError(
        error instanceof Error
          ? error.message
          : "Failed to fetch featured products"
      );
      setProducts([]);
    } finally {
      setLoading(false);
    }
  }, [limit]);

  useEffect(() => {
    fetchFeaturedProducts();
  }, [fetchFeaturedProducts]);

  const refetch = useCallback(() => {
    fetchFeaturedProducts();
  }, [fetchFeaturedProducts]);

  return {
    products,
    loading,
    error,
    refetch,
  };
}

// Trending products hook
export function useTrendingProducts(limit: number = 8) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTrendingProducts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await ProductService.getTrendingProducts(limit);
      setProducts(response.data);
    } catch (error) {
      setError(
        error instanceof Error
          ? error.message
          : "Failed to fetch trending products"
      );
      setProducts([]);
    } finally {
      setLoading(false);
    }
  }, [limit]);

  useEffect(() => {
    fetchTrendingProducts();
  }, [fetchTrendingProducts]);

  const refetch = useCallback(() => {
    fetchTrendingProducts();
  }, [fetchTrendingProducts]);

  return {
    products,
    loading,
    error,
    refetch,
  };
}

// Related products hook
export function useRelatedProducts(
  productId: string | null,
  limit: number = 4
) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchRelatedProducts = useCallback(async () => {
    if (!productId) {
      setProducts([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await ProductService.getRelatedProducts(
        productId,
        limit
      );
      setProducts(response.data || []);
    } catch (error) {
      setError(
        error instanceof Error
          ? error.message
          : "Failed to fetch related products"
      );
      setProducts([]);
    } finally {
      setLoading(false);
    }
  }, [productId, limit]);

  useEffect(() => {
    fetchRelatedProducts();
  }, [fetchRelatedProducts]);

  const refetch = useCallback(() => {
    fetchRelatedProducts();
  }, [fetchRelatedProducts]);

  return {
    products,
    loading,
    error,
    refetch,
  };
}
