# Custom Hooks Implementation - NS Shop

## Tổng quan

Đã hoàn thành việc triển khai hệ thống custom hooks cho NS Shop, tích hợp với service layer để cung cấp data fetching và state management cho các React components.

## Hooks đã triển khai

### 1. Product Hooks (`src/hooks/use-products.ts`)

**Hooks chính:**
- `useProducts(params)` - Fetch danh sách products với pagination và filtering
- `useProduct(id)` - Fetch single product theo ID
- `useProductBySlug(slug)` - Fetch product theo slug, tự động add vào recently viewed
- `useFeaturedProducts(limit)` - Fetch featured products
- `useTrendingProducts(limit)` - Fetch trending products

**Features:**
- Tích hợp với ProductService
- State management với loading, error, data
- Automatic refetch capabilities
- Optimized với useCallback và useMemo

### 2. Category Hooks (`src/hooks/use-categories.ts`)

**Hooks chính:**
- `useCategories(params)` - Fetch danh sách categories với pagination
- `useCategory(id)` - Fetch single category theo ID
- `useCategoryBySlug(slug)` - Fetch category theo slug
- `useRootCategories()` - Fetch root categories (không có parent)
- `useCategoryTree()` - Fetch category tree structure
- `useCategoriesWithCounts()` - Fetch categories với product counts
- `useFeaturedCategories(limit)` - Fetch featured categories

**Features:**
- Tích hợp với CategoryService
- Support cho category hierarchy
- Product count integration
- Tree structure building

### 3. Cart Hooks (`src/hooks/use-cart.ts`)

**Hooks chính:**
- `useCart()` - Main cart management hook
- `useCartItemCount()` - Lightweight cart item count
- `useQuickAddToCart()` - Quick add to cart functionality
- `useCartValidation(cart)` - Cart validation utilities
- `useGuestCartMerge()` - Guest cart merge functionality

**Features:**
- Tích hợp với CartService
- Cart summary calculations
- Optimistic updates
- Guest cart support
- Cart validation
- Real-time cart state management

### 4. User Hooks (`src/hooks/use-user.ts`)

**Hooks chính:**
- `useUser()` - Main user profile management
- `useUserAddresses()` - User addresses management
- `useUserWishlist()` - Wishlist management
- `useUserPreferences()` - User preferences management

**Features:**
- Profile update capabilities
- Address CRUD operations
- Wishlist add/remove
- Avatar upload
- Password change
- Preferences management

### 5. Order Hooks (`src/hooks/use-orders.ts`)

**Hooks chính:**
- `useOrders(params)` - Orders list với pagination
- `useOrder(id)` - Single order details
- `useOrderByTracking(trackingNumber)` - Order tracking
- `useCreateOrder()` - Order creation
- `useOrderStats()` - Order statistics
- `useOrderTimeline(order)` - Order timeline
- `useOrderStatus(order)` - Order status utilities
- `useCancelOrder()` - Order cancellation
- `useReturnOrder()` - Order return
- `useRecentOrders(limit)` - Recent orders

**Features:**
- Complete order lifecycle management
- Order tracking và status
- Statistics và analytics
- Timeline generation
- Cancel/return capabilities

### 6. Search Hooks (`src/hooks/use-search.ts`)

**Hooks chính:**
- `useSearch()` - Main search functionality
- `useSearchSuggestions(query, delay)` - Search suggestions với debouncing
- `useSearchHistory()` - Search history management
- `usePopularSearches()` - Popular searches
- `useAdvancedSearch()` - Advanced search với filters

**Features:**
- Real-time search suggestions
- Search history với localStorage
- Popular searches tracking
- Advanced filtering
- Debounced search input
- Search result caching

## Kiến trúc và Pattern

### 1. Consistent State Management
```typescript
interface UseEntityState {
  data: Entity[];
  total: number;
  page: number;
  totalPages: number;
  loading: boolean;
  error: string | null;
}
```

### 2. Service Integration
- Tất cả hooks đều tích hợp với service layer
- Consistent error handling
- Automatic loading states
- Optimized API calls

### 3. Performance Optimization
- `useCallback` cho functions
- `useMemo` cho computed values
- Debouncing cho search
- Caching strategies

### 4. Error Handling
- Structured error messages
- Graceful fallbacks
- User-friendly error states

## Export Structure

Tất cả hooks được export từ `src/hooks/index.ts`:

```typescript
// Product hooks
export { useProducts, useProduct, useProductBySlug, useFeaturedProducts, useTrendingProducts } from "./use-products";

// Category hooks
export { useCategories, useCategory, useCategoryBySlug, useRootCategories, useCategoryTree, useCategoriesWithCounts, useFeaturedCategories } from "./use-categories";

// Cart hooks
export { useCart, useCartItemCount, useQuickAddToCart, useCartValidation, useGuestCartMerge } from "./use-cart";

// User hooks
export { useUser, useUserAddresses, useUserWishlist, useUserPreferences } from "./use-user";

// Order hooks
export { useOrders, useOrder, useOrderByTracking, useCreateOrder, useOrderStats, useOrderTimeline, useOrderStatus, useCancelOrder, useReturnOrder, useRecentOrders } from "./use-orders";

// Search hooks
export { useSearch, useSearchSuggestions, useSearchHistory, usePopularSearches, useAdvancedSearch } from "./use-search";
```

## Sử dụng trong Components

### Example: Product List Component
```typescript
import { useProducts, useFeaturedProducts } from "@/hooks";

function ProductList() {
  const { data: products, loading, error, refetch } = useProducts({
    page: 1,
    limit: 12,
    sortBy: "createdAt",
    sortOrder: "desc"
  });

  const { products: featured } = useFeaturedProducts(8);

  if (loading) return <Loading />;
  if (error) return <Error message={error} />;

  return (
    <div>
      <FeaturedSection products={featured} />
      <ProductGrid products={products} />
    </div>
  );
}
```

### Example: Cart Management
```typescript
import { useCart, useQuickAddToCart } from "@/hooks";

function CartButton({ productId }: { productId: string }) {
  const { itemCount, totalQuantity } = useCart();
  const { quickAdd, loading } = useQuickAddToCart();

  const handleAddToCart = async () => {
    try {
      await quickAdd(productId, 1);
      toast.success("Added to cart!");
    } catch (error) {
      toast.error("Failed to add to cart");
    }
  };

  return (
    <button onClick={handleAddToCart} disabled={loading}>
      Add to Cart ({itemCount})
    </button>
  );
}
```

## Lỗi cần sửa

Hiện tại có một số lỗi TypeScript cần được sửa:

1. **Service Layer Types**: Một số types trong services chưa match với database schema
2. **User Gender Field**: Property 'gender' không tồn tại trong User type
3. **Optional vs Undefined**: Một số API responses trả về undefined thay vì null

## Bước tiếp theo

1. **Sửa TypeScript errors** - Cập nhật types để match với database schema
2. **Context Integration** - Tích hợp hooks với React contexts
3. **Component Integration** - Áp dụng hooks vào các components hiện có
4. **Testing** - Viết tests cho các hooks
5. **Performance Optimization** - Implement caching và optimization strategies

## Kết luận

Hệ thống custom hooks đã được triển khai hoàn chỉnh với:
- ✅ 6 nhóm hooks chính (Products, Categories, Cart, User, Orders, Search)
- ✅ 30+ hooks cụ thể cho các use cases khác nhau
- ✅ Tích hợp hoàn chỉnh với service layer
- ✅ Consistent patterns và error handling
- ✅ Performance optimization
- ⚠️ Cần sửa một số TypeScript errors
- ⏳ Chờ integration với components và contexts

Hooks system này cung cấp foundation mạnh mẽ cho data management trong NS Shop application.
