"use client";

import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Star,
  Heart,
  ShoppingCart,
  TrendingUp,
  Eye,
  Loader2,
  AlertCircle,
} from "lucide-react";
import { ProductImage } from "@/components/ui/client-image";
import { formatCurrency } from "@/lib/utils";

import { useTrendingProducts } from "@/hooks";
import { useEnhancedCart, useUserContext } from "@/contexts";

// Helper function to get product image
function getProductImage(product: any): string {
  // Check for new media relationship first
  if (product.media && product.media.length > 0) {
    const primaryImage =
      product.media.find((m: any) => m.isPrimary) || product.media[0];
    return primaryImage.media.url;
  }

  // Fallback to legacy images field
  if (product.images && product.images.length > 0) {
    return product.images[0];
  }

  // Default fallback
  return "/images/placeholder.svg";
}

// Helper function to determine if product is new (created within last 30 days)
function isNewProduct(createdAt: string): boolean {
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  return new Date(createdAt) > thirtyDaysAgo;
}

const _sampleTrendingProducts = [
  {
    id: 1,
    name: "Áo blazer nữ công sở",
    slug: "ao-blazer-nu-cong-so",
    price: 899000,
    salePrice: 699000,
    image: "/images/products/ao-blazer-nu-cong-so.svg",
    rating: 4.9,
    reviews: 234,
    isNew: false,
    isTrending: true,
    trendingRank: 1,
    viewCount: 1250,
    soldCount: 89,
    tags: ["Bestseller", "Trending"],
  },
  {
    id: 2,
    name: "Giày sneaker unisex",
    slug: "giay-sneaker-unisex",
    price: 1299000,
    salePrice: null,
    image: "/images/products/giay-sneaker-unisex.svg",
    rating: 4.8,
    reviews: 156,
    isNew: true,
    isTrending: true,
    trendingRank: 2,
    viewCount: 980,
    soldCount: 67,
    tags: ["New", "Trending"],
  },
  {
    id: 3,
    name: "Túi xách da thật cao cấp",
    slug: "tui-xach-da-that-cao-cap",
    price: 1599000,
    salePrice: 1299000,
    image: "/images/products/tui-xach-da-that-cao-cap.svg",
    rating: 4.7,
    reviews: 98,
    isNew: false,
    isTrending: true,
    trendingRank: 3,
    viewCount: 756,
    soldCount: 45,
    tags: ["Sale", "Premium"],
  },
  {
    id: 4,
    name: "Đồng hồ thông minh",
    slug: "dong-ho-thong-minh",
    price: 2499000,
    salePrice: null,
    image: "/images/products/dong-ho-thong-minh.svg",
    rating: 4.9,
    reviews: 189,
    isNew: true,
    isTrending: true,
    trendingRank: 4,
    viewCount: 1100,
    soldCount: 78,
    tags: ["New", "Tech"],
  },
];

export function TrendingProducts() {
  const { products, loading, error } = useTrendingProducts(4);
  const { quickAdd, quickAddLoading, isInCart } = useEnhancedCart();
  const { addToWishlist, removeFromWishlist, isInWishlist } = useUserContext();

  const toggleWishlist = (productId: string) => {
    if (isInWishlist(productId)) {
      removeFromWishlist(productId);
    } else {
      addToWishlist(productId);
    }
  };

  return (
    <section className="py-16 lg:py-24 bg-gradient-to-br from-background via-muted/30 to-background">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-fashion-500/10 to-fashion-600/10 px-4 py-2 rounded-full mb-4">
            <TrendingUp className="h-4 w-4 text-fashion-500" />
            <span className="text-sm font-medium text-fashion-600">
              Xu hướng hot nhất
            </span>
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            Sản phẩm đang <span className="text-fashion-600">trending</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Khám phá những sản phẩm được quan tâm và mua nhiều nhất trong tuần
            này
          </p>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-muted-foreground">
              Đang tải sản phẩm trending...
            </span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="flex justify-center items-center py-12">
            <AlertCircle className="h-8 w-8 text-red-500" />
            <span className="ml-2 text-red-500">
              Có lỗi xảy ra khi tải sản phẩm
            </span>
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && products.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">
              Chưa có sản phẩm trending nào
            </p>
          </div>
        )}

        {/* Products Grid */}
        {!loading && !error && products.length > 0 && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
            {products.map((product, index) => (
              <Card
                key={product.id}
                className="group overflow-hidden border-0 shadow-sm hover:shadow-xl transition-all duration-500 bg-white/80 backdrop-blur-sm"
              >
                <CardContent className="p-0">
                  {/* Product Image */}
                  <div className="relative overflow-hidden bg-muted">
                    <div className="aspect-[4/5] relative">
                      <ProductImage
                        src={getProductImage(product)}
                        alt={product.name}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                        fallbackSrc="/images/placeholder.svg"
                      />

                      {/* Trending Rank Badge */}
                      <div className="absolute top-3 left-3 bg-gradient-to-r from-fashion-500 to-fashion-600 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center space-x-1">
                        <TrendingUp className="h-3 w-3" />
                        <span>#{index + 1}</span>
                      </div>

                      {/* Tags */}
                      <div className="absolute top-3 right-3 flex flex-col space-y-1">
                        {isNewProduct(product.createdAt.toString()) && (
                          <Badge variant="secondary" className="text-xs">
                            Mới
                          </Badge>
                        )}
                        {product.featured && (
                          <Badge variant="secondary" className="text-xs">
                            Nổi bật
                          </Badge>
                        )}
                      </div>

                      {/* Sale Badge */}
                      {product.salePrice && (
                        <div className="absolute bottom-3 left-3 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
                          -
                          {Math.round(
                            ((product.price - product.salePrice) /
                              product.price) *
                              100
                          )}
                          %
                        </div>
                      )}

                      {/* Quick Actions */}
                      <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-2">
                        <Button
                          size="sm"
                          variant="secondary"
                          className="bg-white/90 hover:bg-white"
                          onClick={() => toggleWishlist(product.id)}
                        >
                          <Heart
                            className={`h-4 w-4 ${
                              isInWishlist(product.id)
                                ? "fill-red-500 text-red-500"
                                : ""
                            }`}
                          />
                        </Button>
                        <Button
                          size="sm"
                          variant="secondary"
                          className="bg-white/90 hover:bg-white"
                          asChild
                        >
                          <Link href={`/products/${product.slug}`}>
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button
                          size="sm"
                          variant="secondary"
                          className="bg-white/90 hover:bg-white"
                          disabled={quickAddLoading || isInCart(product.id)}
                          onClick={() => quickAdd(product.id)}
                        >
                          <ShoppingCart className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Product Info */}
                  <div className="p-4">
                    <Link href={`/products/${product.slug}`}>
                      <h3 className="font-semibold text-sm lg:text-base mb-2 hover:text-primary transition-colors line-clamp-2">
                        {product.name}
                      </h3>
                    </Link>

                    {/* Stats */}
                    <div className="flex items-center justify-between text-xs text-muted-foreground mb-2">
                      <div className="flex items-center space-x-1">
                        <Eye className="h-3 w-3" />
                        <span>Trending</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <ShoppingCart className="h-3 w-3" />
                        <span>Nổi bật</span>
                      </div>
                    </div>

                    {/* Rating */}
                    <div className="flex items-center gap-1 mb-2">
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-3 w-3 ${
                              i < Math.floor(product.avgRating)
                                ? "text-yellow-400 fill-current"
                                : "text-gray-300"
                            }`}
                          />
                        ))}
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {product.avgRating.toFixed(1)} ({product.reviewCount})
                      </span>
                    </div>

                    {/* Price */}
                    <div className="flex items-center gap-2">
                      {product.salePrice ? (
                        <>
                          <span className="font-bold text-primary">
                            {formatCurrency(product.salePrice)}
                          </span>
                          <span className="text-sm text-muted-foreground line-through">
                            {formatCurrency(product.price)}
                          </span>
                        </>
                      ) : (
                        <span className="font-bold text-primary">
                          {formatCurrency(product.price)}
                        </span>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* View All Button */}
        {!loading && !error && products.length > 0 && (
          <div className="text-center mt-12">
            <Button asChild variant="fashion" size="lg" className="group">
              <Link href="/products?filter=trending">
                Xem tất cả sản phẩm trending
                <TrendingUp className="ml-2 h-4 w-4 transition-transform group-hover:scale-110" />
              </Link>
            </Button>
          </div>
        )}
      </div>
    </section>
  );
}
