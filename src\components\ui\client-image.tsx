"use client";

import React, { useState } from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { ImageIcon, AlertCircle, Loader2 } from "lucide-react";

interface ClientImageProps {
  src?: string | string[] | null;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  fallbackIcon?: React.ReactNode;
  showLoadingState?: boolean;
  rounded?: boolean;
  objectFit?: "cover" | "contain" | "fill" | "none" | "scale-down";
  priority?: boolean;
  sizes?: string;
  fill?: boolean;
  fallbackSrc?: string;
}

export function ClientImage({
  src,
  alt,
  width,
  height,
  className,
  fallbackIcon,
  showLoadingState = true,
  rounded = true,
  objectFit = "cover",
  priority = false,
  sizes,
  fill = false,
  fallbackSrc,
}: ClientImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [currentSrcIndex, setCurrentSrcIndex] = useState(0);

  // Get the actual image source
  const getImageSrc = (): string | null => {
    if (!src) return null;

    if (Array.isArray(src)) {
      return src[currentSrcIndex] || null;
    }

    return src;
  };

  // Validate URL function
  const isValidUrl = (url: string | null | undefined): boolean => {
    if (!url || typeof url !== "string" || url.trim() === "") {
      return false;
    }

    try {
      // Check if it's a valid URL
      new URL(url);
      return true;
    } catch {
      // Check if it's a valid relative path
      return (
        url.startsWith("/") || url.startsWith("./") || url.startsWith("../")
      );
    }
  };

  const handleLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const handleError = () => {
    setIsLoading(false);

    // If we have multiple sources, try the next one
    if (Array.isArray(src) && currentSrcIndex < src.length - 1) {
      setCurrentSrcIndex(currentSrcIndex + 1);
      setIsLoading(true);
      setHasError(false);
      return;
    }

    // If we have a fallback source, try it
    if (fallbackSrc && getImageSrc() !== fallbackSrc) {
      setCurrentSrcIndex(0);
      setIsLoading(true);
      setHasError(false);
      // We'll handle fallbackSrc in the render logic
      return;
    }

    setHasError(true);
  };

  const containerClasses = cn(
    "relative bg-muted flex items-center justify-center overflow-hidden",
    rounded && "rounded-lg",
    fill && "aspect-square", // Ensure aspect ratio when using fill
    className
  );

  const imageClasses = cn(
    "transition-opacity duration-200",
    isLoading && "opacity-0",
    objectFit === "cover" && "object-cover",
    objectFit === "contain" && "object-contain",
    objectFit === "fill" && "object-fill",
    objectFit === "none" && "object-none",
    objectFit === "scale-down" && "object-scale-down"
  );

  // Get the current source to display
  const currentSrc = getImageSrc();
  const shouldUseFallback =
    hasError && fallbackSrc && currentSrc !== fallbackSrc;
  const finalSrc = shouldUseFallback ? fallbackSrc : currentSrc;

  // If no src provided, invalid URL, or has error (and no fallback), show fallback
  if (!finalSrc || !isValidUrl(finalSrc) || (hasError && !shouldUseFallback)) {
    return (
      <div className={containerClasses} style={{ width, height }}>
        {hasError ? (
          <div className="flex flex-col items-center justify-center text-muted-foreground">
            <AlertCircle className="h-4 w-4 mb-1" />
            <span className="text-xs">Lỗi tải ảnh</span>
          </div>
        ) : (
          fallbackIcon || (
            <ImageIcon className="h-6 w-6 text-muted-foreground" />
          )
        )}
      </div>
    );
  }

  return (
    <div
      className={containerClasses}
      style={!fill ? { width, height } : undefined}
    >
      {/* Loading state */}
      {isLoading && showLoadingState && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
        </div>
      )}

      {/* Image */}
      {fill ? (
        <Image
          src={finalSrc}
          alt={alt}
          fill
          className={imageClasses}
          onLoad={handleLoad}
          onError={handleError}
          priority={priority}
          sizes={sizes}
        />
      ) : width && height ? (
        <Image
          src={finalSrc}
          alt={alt}
          width={width}
          height={height}
          className={imageClasses}
          onLoad={handleLoad}
          onError={handleError}
          priority={priority}
          sizes={sizes}
        />
      ) : (
        <Image
          src={finalSrc}
          alt={alt}
          fill
          className={imageClasses}
          onLoad={handleLoad}
          onError={handleError}
          priority={priority}
          sizes={sizes}
        />
      )}
    </div>
  );
}

// Specialized variants for common use cases
export function ProductImage({
  src,
  alt,
  className,
  fallbackSrc = "/images/placeholder.jpg",
  ...props
}: Omit<ClientImageProps, "fallbackIcon"> & { fallbackSrc?: string }) {
  return (
    <ClientImage
      src={src}
      alt={alt}
      className={cn("flex-shrink-0", className)}
      fallbackIcon={<ImageIcon className="h-6 w-6 text-muted-foreground" />}
      fallbackSrc={fallbackSrc}
      {...props}
    />
  );
}

export function CategoryImage({
  src,
  alt,
  className,
  ...props
}: Omit<ClientImageProps, "fallbackIcon">) {
  return (
    <ClientImage
      src={src}
      alt={alt}
      className={cn("flex-shrink-0", className)}
      fallbackIcon={<ImageIcon className="h-6 w-6 text-muted-foreground" />}
      {...props}
    />
  );
}

export function BrandLogo({
  src,
  alt,
  className,
  ...props
}: Omit<ClientImageProps, "fallbackIcon" | "objectFit">) {
  return (
    <ClientImage
      src={src}
      alt={alt}
      className={cn("flex-shrink-0", className)}
      objectFit="contain"
      fallbackIcon={
        <div className="text-xs font-bold text-muted-foreground">LOGO</div>
      }
      {...props}
    />
  );
}
