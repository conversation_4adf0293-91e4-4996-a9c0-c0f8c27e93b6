"use client";

import { ApiResponse, PaginatedResponse } from "@/types";

// API Error class for better error handling
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message);
    this.name = "ApiError";
  }
}

// Request configuration interface
interface RequestConfig {
  method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  headers?: Record<string, string>;
  body?: any;
  cache?: RequestCache;
  next?: NextFetchRequestConfig;
}

// Cache configuration
interface CacheConfig {
  ttl?: number; // Time to live in milliseconds
  key?: string; // Custom cache key
}

// Simple in-memory cache
class SimpleCache {
  private cache = new Map<string, { data: any; expires: number }>();

  set(key: string, data: any, ttl: number = 5 * 60 * 1000) {
    this.cache.set(key, {
      data,
      expires: Date.now() + ttl,
    });
  }

  get(key: string) {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() > item.expires) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  delete(key: string) {
    this.cache.delete(key);
  }

  clear() {
    this.cache.clear();
  }
}

const cache = new SimpleCache();

// Base API client class
class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = "/api") {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    config: RequestConfig = {},
    cacheConfig?: CacheConfig
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const cacheKey = cacheConfig?.key || `${config.method || "GET"}:${url}`;

    // Check cache for GET requests
    if ((!config.method || config.method === "GET") && cacheConfig) {
      const cached = cache.get(cacheKey);
      if (cached) {
        return cached;
      }
    }

    const requestConfig: RequestInit = {
      method: config.method || "GET",
      headers: {
        "Content-Type": "application/json",
        ...config.headers,
      },
      cache: config.cache,
      next: config.next,
    };

    if (config.body && config.method !== "GET") {
      requestConfig.body = JSON.stringify(config.body);
    }

    try {
      const response = await fetch(url, requestConfig);

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        let errorCode = response.status.toString();

        try {
          const errorData = await response.json();
          if (errorData.error) {
            errorMessage = errorData.error;
          }
          if (errorData.code) {
            errorCode = errorData.code;
          }
        } catch {
          // If we can't parse error JSON, use default message
        }

        throw new ApiError(errorMessage, response.status, errorCode);
      }

      const data = await response.json();

      // Cache successful GET responses
      if ((!config.method || config.method === "GET") && cacheConfig) {
        cache.set(cacheKey, data, cacheConfig.ttl);
      }

      return data;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      // Network or other errors
      throw new ApiError(
        error instanceof Error ? error.message : "Network error occurred",
        0,
        "NETWORK_ERROR"
      );
    }
  }

  // GET request
  async get<T>(
    endpoint: string,
    params?: Record<string, any>,
    cacheConfig?: CacheConfig
  ): Promise<T> {
    let url = endpoint;
    if (params) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, value.toString());
        }
      });
      url += `?${searchParams.toString()}`;
    }

    return this.request<T>(url, { method: "GET" }, cacheConfig);
  }

  // POST request
  async post<T>(endpoint: string, data?: any): Promise<T> {
    // Clear related cache entries
    this.clearCacheByPattern(endpoint);
    return this.request<T>(endpoint, { method: "POST", body: data });
  }

  // PUT request
  async put<T>(endpoint: string, data?: any): Promise<T> {
    // Clear related cache entries
    this.clearCacheByPattern(endpoint);
    return this.request<T>(endpoint, { method: "PUT", body: data });
  }

  // DELETE request
  async delete<T>(endpoint: string): Promise<T> {
    // Clear related cache entries
    this.clearCacheByPattern(endpoint);
    return this.request<T>(endpoint, { method: "DELETE" });
  }

  // PATCH request
  async patch<T>(endpoint: string, data?: any): Promise<T> {
    // Clear related cache entries
    this.clearCacheByPattern(endpoint);
    return this.request<T>(endpoint, { method: "PATCH", body: data });
  }

  // Clear cache by pattern
  private clearCacheByPattern(pattern: string) {
    const keys = Array.from(cache["cache"].keys());
    keys.forEach((key) => {
      if (key.includes(pattern)) {
        cache.delete(key);
      }
    });
  }

  // Clear all cache
  clearCache() {
    cache.clear();
  }

  // Clear specific cache entry
  clearCacheEntry(key: string) {
    cache.delete(key);
  }
}

// Create singleton instance
export const apiClient = new ApiClient();

// Utility functions for common API patterns
export const api = {
  // Generic CRUD operations
  async getList<T>(
    endpoint: string,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
      sort?: string;
      filter?: Record<string, any>;
    }
  ): Promise<PaginatedResponse<T>> {
    return apiClient.get<PaginatedResponse<T>>(endpoint, params, {
      ttl: 2 * 60 * 1000, // 2 minutes cache
    });
  },

  async getById<T>(endpoint: string, id: string): Promise<ApiResponse<T>> {
    return apiClient.get<ApiResponse<T>>(`${endpoint}/${id}`, undefined, {
      ttl: 5 * 60 * 1000, // 5 minutes cache
    });
  },

  async create<T>(endpoint: string, data: any): Promise<ApiResponse<T>> {
    return apiClient.post<ApiResponse<T>>(endpoint, data);
  },

  async update<T>(
    endpoint: string,
    id: string,
    data: any
  ): Promise<ApiResponse<T>> {
    return apiClient.put<ApiResponse<T>>(`${endpoint}/${id}`, data);
  },

  async remove<T>(endpoint: string, id: string): Promise<ApiResponse<T>> {
    return apiClient.delete<ApiResponse<T>>(`${endpoint}/${id}`);
  },

  // Specialized methods
  async search<T>(
    query: string,
    filters?: Record<string, any>
  ): Promise<PaginatedResponse<T>> {
    return apiClient.get<PaginatedResponse<T>>("/search", {
      q: query,
      ...filters,
    });
  },

  // Cache management
  clearCache: () => apiClient.clearCache(),
  clearCacheEntry: (key: string) => apiClient.clearCacheEntry(key),
};

// Export types
export type { RequestConfig, CacheConfig };
