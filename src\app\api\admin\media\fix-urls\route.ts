import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/lib/admin-auth";
import { PrismaClient } from "@prisma/client";
import { getMinioConfig } from "@/lib/debug-env";

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken || adminToken.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    console.log("🔍 Scanning for media URLs with undefined values...");
    
    // Get all media records with URLs containing "undefined"
    const mediaWithBadUrls = await prisma.media.findMany({
      where: {
        url: {
          contains: "undefined"
        }
      }
    });

    console.log(`📊 Found ${mediaWithBadUrls.length} media records with bad URLs`);

    if (mediaWithBadUrls.length === 0) {
      return NextResponse.json({
        success: true,
        message: "No URLs need fixing!",
        fixedCount: 0,
        totalFound: 0,
      });
    }

    const config = getMinioConfig();
    let fixedCount = 0;
    const errors: string[] = [];

    for (const media of mediaWithBadUrls) {
      try {
        // Extract the path from the bad URL
        const path = media.url.replace(/^.*\/ns-shop-media\//, "");
        const newUrl = `http://${config.endpoint}:${config.port}/${config.bucketName}/${path}`;

        console.log(`🔧 Fixing: ${media.url} -> ${newUrl}`);

        // Update the URL in database
        await prisma.media.update({
          where: { id: media.id },
          data: { url: newUrl }
        });

        fixedCount++;
      } catch (error) {
        const errorMsg = `Error fixing URL for media ${media.id}: ${error}`;
        console.error(`❌ ${errorMsg}`);
        errors.push(errorMsg);
      }
    }

    console.log(`✅ Successfully fixed ${fixedCount} media URLs`);

    return NextResponse.json({
      success: true,
      message: `Successfully fixed ${fixedCount} media URLs`,
      fixedCount,
      totalFound: mediaWithBadUrls.length,
      errors: errors.length > 0 ? errors : undefined,
    });
    
  } catch (error) {
    console.error("❌ Error running fix script:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi sửa URLs" },
      { status: 500 }
    );
  }
}
