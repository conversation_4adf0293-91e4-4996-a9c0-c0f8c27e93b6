"use client";

import Link from "next/link";
import {
  Instagram,
  Facebook,
  Youtube,
  Twitter,
  Heart,
  MessageCircle,
  Share2,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ClientImage } from "@/components/ui/client-image";

const socialPosts = [
  {
    id: 1,
    platform: "instagram",
    image: "/images/social/post-1.jpg",
    likes: 1234,
    comments: 89,
    caption: "Bộ sưu tập mùa thu đầy màu sắc 🍂 #NSShop #Fashion #Autumn",
    url: "https://instagram.com/nsshop",
  },
  {
    id: 2,
    platform: "instagram",
    image: "/images/social/post-2.jpg",
    likes: 2156,
    comments: 156,
    caption: "Street style với áo thun cotton premium ✨ #StreetStyle #Cotton",
    url: "https://instagram.com/nsshop",
  },
  {
    id: 3,
    platform: "instagram",
    image: "/images/social/post-3.jpg",
    likes: 987,
    comments: 67,
    caption: "<PERSON><PERSON><PERSON> maxi cho ngày hè tươi mát 🌞 #Summer #MaxiDress",
    url: "https://instagram.com/nsshop",
  },
  {
    id: 4,
    platform: "instagram",
    image: "/images/social/post-4.jpg",
    likes: 1876,
    comments: 234,
    caption: "Phong cách công sở thanh lịch 💼 #Office #Professional",
    url: "https://instagram.com/nsshop",
  },
  {
    id: 5,
    platform: "instagram",
    image: "/images/social/post-5.jpg",
    likes: 1543,
    comments: 123,
    caption: "Accessories hoàn thiện outfit 👜 #Accessories #Style",
    url: "https://instagram.com/nsshop",
  },
  {
    id: 6,
    platform: "instagram",
    image: "/images/social/post-6.jpg",
    likes: 2341,
    comments: 189,
    caption: "Sneaker trắng - must have item 👟 #Sneakers #White #MustHave",
    url: "https://instagram.com/nsshop",
  },
];

const socialLinks = [
  {
    name: "Instagram",
    icon: <Instagram className="h-6 w-6" />,
    url: "https://instagram.com/nsshop",
    followers: "125K",
    color: "bg-gradient-to-br from-purple-500 via-pink-500 to-orange-400",
  },
  {
    name: "Facebook",
    icon: <Facebook className="h-6 w-6" />,
    url: "https://facebook.com/nsshop",
    followers: "89K",
    color: "bg-blue-600",
  },
  {
    name: "YouTube",
    icon: <Youtube className="h-6 w-6" />,
    url: "https://youtube.com/nsshop",
    followers: "45K",
    color: "bg-red-600",
  },
  {
    name: "Twitter",
    icon: <Twitter className="h-6 w-6" />,
    url: "https://twitter.com/nsshop",
    followers: "67K",
    color: "bg-sky-500",
  },
];

export function ModernSocialSection() {
  return (
    <section className="py-20 lg:py-32 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-white rounded-full px-4 py-2 mb-6 shadow-sm">
            <div className="w-2 h-2 bg-black rounded-full"></div>
            <span className="text-sm font-medium text-gray-700">
              Mạng xã hội
            </span>
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Kết nối với chúng tôi
            <br />
            <span className="text-gray-600">trên mạng xã hội</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Theo dõi chúng tôi để cập nhật xu hướng thời trang mới nhất và những
            ưu đãi đặc biệt.
          </p>
        </div>

        {/* Social Links */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {socialLinks.map((social) => (
            <Link
              key={social.name}
              href={social.url}
              target="_blank"
              rel="noopener noreferrer"
            >
              <Card className="group cursor-pointer border-0 bg-white hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <div
                    className={`w-16 h-16 ${social.color} rounded-2xl flex items-center justify-center mx-auto mb-4 text-white group-hover:scale-110 transition-transform duration-300`}
                  >
                    {social.icon}
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-1">
                    {social.name}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {social.followers} followers
                  </p>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {/* Instagram Feed */}
        <div className="mb-16">
          <div className="text-center mb-8">
            <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
              #NSShop trên Instagram
            </h3>
            <p className="text-gray-600">
              Khám phá cách khách hàng phối đồ với sản phẩm của chúng tôi
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {socialPosts.map((post) => (
              <Link
                key={post.id}
                href={post.url}
                target="_blank"
                rel="noopener noreferrer"
              >
                <Card className="group cursor-pointer border-0 bg-white overflow-hidden hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-0">
                    <div className="relative aspect-square overflow-hidden">
                      <ClientImage
                        src={post.image}
                        alt="Instagram post"
                        fill
                        className="object-cover group-hover:scale-110 transition-transform duration-500"
                        fallbackSrc="/images/placeholder.jpg"
                      />

                      {/* Overlay */}
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />

                      {/* Hover Content */}
                      <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="text-white text-center">
                          <div className="flex items-center justify-center gap-4 mb-2">
                            <div className="flex items-center gap-1">
                              <Heart className="h-4 w-4 fill-current" />
                              <span className="text-sm font-medium">
                                {post.likes.toLocaleString()}
                              </span>
                            </div>
                            <div className="flex items-center gap-1">
                              <MessageCircle className="h-4 w-4" />
                              <span className="text-sm font-medium">
                                {post.comments}
                              </span>
                            </div>
                          </div>
                          <Instagram className="h-6 w-6 mx-auto" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>

        {/* Follow CTA */}
        <div className="text-center">
          <div className="bg-white rounded-3xl p-8 lg:p-12 shadow-sm border border-gray-100">
            <div className="max-w-2xl mx-auto">
              <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
                Đừng bỏ lỡ những xu hướng mới nhất!
              </h3>
              <p className="text-gray-600 mb-8">
                Theo dõi chúng tôi trên các mạng xã hội để cập nhật những bộ sưu
                tập mới, tips phối đồ và những ưu đãi độc quyền.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  className="bg-gradient-to-r from-purple-500 via-pink-500 to-orange-400 hover:from-purple-600 hover:via-pink-600 hover:to-orange-500 text-white px-8 py-3 font-medium"
                  asChild
                >
                  <Link
                    href="https://instagram.com/nsshop"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Instagram className="h-5 w-5 mr-2" />
                    Theo dõi Instagram
                  </Link>
                </Button>
                <Button
                  variant="outline"
                  className="border-2 border-gray-300 hover:border-gray-400 px-8 py-3 font-medium"
                  asChild
                >
                  <Link
                    href="https://facebook.com/nsshop"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Facebook className="h-5 w-5 mr-2" />
                    Like Facebook
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
