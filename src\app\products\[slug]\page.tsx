"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@/components/layout";
import { ClientImage } from "@/components/ui/client-image";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import {
  Star,
  Heart,
  ShoppingCart,
  Minus,
  Plus,
  Truck,
  Shield,
  RotateCcw,
  ChevronLeft,
  ChevronRight,
  ZoomIn,
  GitCompare,
  Eye,
  Facebook,
  Twitter,
  Copy,
  Check,
  Award,
  Users,
  TrendingUp,
} from "lucide-react";
import { toast } from "sonner";
import { formatCurrency } from "@/lib/utils";
import { useProductBySlug, useRelatedProducts } from "@/hooks/use-products";
import { useEnhancedCart } from "@/contexts/enhanced-cart-context";
import { useUserContext } from "@/contexts/user-context";

export default function ProductDetailPage() {
  const params = useParams();
  const slug = params.slug as string;

  // Hooks
  const { product, loading } = useProductBySlug(slug);
  const { products: relatedProducts } = useRelatedProducts(product?.id || null);
  const { quickAdd, quickAddLoading } = useEnhancedCart();
  const { addToWishlist, isInWishlist } = useUserContext();

  // Local state
  const [quantity, setQuantity] = useState(1);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [selectedVariants] = useState<{
    [key: string]: string;
  }>({});
  const [isImageZoomed, setIsImageZoomed] = useState(false);
  const [copiedLink, setCopiedLink] = useState(false);

  // Wishlist state
  const isWishlisted = product ? isInWishlist(product.id) : false;

  // Helper functions

  const toggleCompare = () => {
    if (!product) return;

    if (compareList.includes(product.id)) {
      setCompareList((prev) => prev.filter((id) => id !== product.id));
      toast.success("Đã xóa khỏi danh sách so sánh");
    } else if (compareList.length < 3) {
      setCompareList((prev) => [...prev, product.id]);
      toast.success("Đã thêm vào danh sách so sánh");
    } else {
      toast.error("Chỉ có thể so sánh tối đa 3 sản phẩm");
    }
  };

  const copyProductLink = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      setCopiedLink(true);
      toast.success("Đã sao chép link sản phẩm");
      setTimeout(() => setCopiedLink(false), 2000);
    } catch {
      toast.error("Không thể sao chép link");
    }
  };

  const shareToSocial = (platform: string) => {
    const url = encodeURIComponent(window.location.href);
    const text = encodeURIComponent(
      `Xem sản phẩm ${product?.name} tại NS Shop`
    );

    let shareUrl = "";
    switch (platform) {
      case "facebook":
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
        break;
      case "twitter":
        shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${text}`;
        break;
      case "instagram":
        // Instagram doesn't support direct URL sharing, so copy to clipboard
        copyProductLink();
        return;
    }

    if (shareUrl) {
      window.open(shareUrl, "_blank", "width=600,height=400");
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating)
            ? "text-yellow-400 fill-current"
            : "text-gray-300"
        }`}
      />
    ));
  };

  const handleAddToCart = async () => {
    if (!product) return;

    try {
      await quickAdd(product.id, quantity, selectedVariants);
      toast.success("Đã thêm sản phẩm vào giỏ hàng");
      // Reset quantity after adding
      setQuantity(1);
    } catch {
      toast.error("Có lỗi xảy ra khi thêm vào giỏ hàng");
    }
  };

  const handleWishlist = async () => {
    if (!product) return;

    try {
      await addToWishlist(product.id);
      toast.success(
        isWishlisted
          ? "Đã xóa khỏi danh sách yêu thích"
          : "Đã thêm vào danh sách yêu thích"
      );
    } catch {
      toast.error("Có lỗi xảy ra");
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="aspect-square bg-gray-200 rounded-lg" />
                <div className="grid grid-cols-4 gap-2">
                  {Array.from({ length: 4 }, (_, i) => (
                    <div
                      key={i}
                      className="aspect-square bg-gray-200 rounded"
                    />
                  ))}
                </div>
              </div>
              <div className="space-y-4">
                <div className="h-8 bg-gray-200 rounded" />
                <div className="h-4 bg-gray-200 rounded w-2/3" />
                <div className="h-6 bg-gray-200 rounded w-1/2" />
                <div className="h-20 bg-gray-200 rounded" />
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold mb-4">Không tìm thấy sản phẩm</h1>
            <Link href="/products">
              <Button>Quay lại danh sách sản phẩm</Button>
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-muted-foreground mb-8">
          <Link href="/" className="hover:text-foreground">
            Trang chủ
          </Link>
          <span>/</span>
          <Link href="/products" className="hover:text-foreground">
            Sản phẩm
          </Link>
          <span>/</span>
          <Link
            href={`/categories/${product.category.slug}`}
            className="hover:text-foreground"
          >
            {product.category.name}
          </Link>
          <span>/</span>
          <span className="text-foreground">{product.name}</span>
        </nav>

        {/* Product Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Enhanced Product Images */}
          <div className="space-y-4">
            {/* Main Image with Zoom */}
            <div className="relative aspect-square overflow-hidden rounded-xl bg-gradient-to-br from-muted to-muted-foreground/10 group">
              <Dialog open={isImageZoomed} onOpenChange={setIsImageZoomed}>
                <DialogTrigger asChild>
                  <div className="relative w-full h-full cursor-zoom-in">
                    <ClientImage
                      src={product.images[selectedImageIndex]}
                      alt={product.name}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                      fallbackSrc="/images/placeholder.jpg"
                    />

                    {/* Badges */}
                    <div className="absolute top-4 left-4 flex flex-col gap-2">
                      {product.salePrice && (
                        <Badge className="bg-red-500 text-white">
                          -
                          {Math.round(
                            ((product.price - product.salePrice) /
                              product.price) *
                              100
                          )}
                          %
                        </Badge>
                      )}
                      {product.isNew && (
                        <Badge className="bg-green-500 text-white">Mới</Badge>
                      )}
                      {product.isBestseller && (
                        <Badge className="bg-orange-500 text-white">
                          Bestseller
                        </Badge>
                      )}
                      {product.isTrending && (
                        <Badge className="bg-fashion-500 text-white">
                          <TrendingUp className="h-3 w-3 mr-1" />
                          Trending
                        </Badge>
                      )}
                    </div>

                    {/* Zoom Icon */}
                    <div className="absolute top-4 right-4 bg-white/80 backdrop-blur-sm rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <ZoomIn className="h-4 w-4" />
                    </div>

                    {/* Navigation Arrows */}
                    {product.images.length > 1 && (
                      <>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedImageIndex(
                              selectedImageIndex === 0
                                ? product.images.length - 1
                                : selectedImageIndex - 1
                            );
                          }}
                          className="absolute left-3 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white rounded-full p-2 transition-all opacity-0 group-hover:opacity-100 shadow-lg"
                        >
                          <ChevronLeft className="h-4 w-4" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedImageIndex(
                              selectedImageIndex === product.images.length - 1
                                ? 0
                                : selectedImageIndex + 1
                            );
                          }}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white rounded-full p-2 transition-all opacity-0 group-hover:opacity-100 shadow-lg"
                        >
                          <ChevronRight className="h-4 w-4" />
                        </button>
                      </>
                    )}

                    {/* Image Counter */}
                    {product.images.length > 1 && (
                      <div className="absolute bottom-4 right-4 bg-black/60 text-white px-2 py-1 rounded text-xs">
                        {selectedImageIndex + 1} / {product.images.length}
                      </div>
                    )}
                  </div>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[90vh] p-0">
                  <div className="relative aspect-square">
                    <ClientImage
                      src={product.images[selectedImageIndex]}
                      alt={product.name}
                      fill
                      className="object-contain"
                      fallbackSrc="/images/placeholder.jpg"
                    />
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            {/* Enhanced Thumbnail Images */}
            {product.images.length > 1 && (
              <div className="grid grid-cols-5 gap-2">
                {product.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`relative aspect-square overflow-hidden rounded-lg border-2 transition-all duration-200 ${
                      selectedImageIndex === index
                        ? "border-fashion-500 ring-2 ring-fashion-200 scale-105"
                        : "border-gray-200 hover:border-gray-300 hover:scale-102"
                    }`}
                  >
                    <ClientImage
                      src={image}
                      alt={`${product.name} ${index + 1}`}
                      fill
                      className="object-cover"
                      fallbackSrc="/images/placeholder.jpg"
                    />
                  </button>
                ))}
              </div>
            )}

            {/* Product Stats */}
            <div className="grid grid-cols-3 gap-4 p-4 bg-muted/30 rounded-lg">
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <Eye className="h-4 w-4 text-muted-foreground mr-1" />
                </div>
                <div className="text-sm font-medium">
                  {product.viewCount || 0}
                </div>
                <div className="text-xs text-muted-foreground">Lượt xem</div>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <ShoppingCart className="h-4 w-4 text-muted-foreground mr-1" />
                </div>
                <div className="text-sm font-medium">
                  {product.soldCount || 0}
                </div>
                <div className="text-xs text-muted-foreground">Đã bán</div>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <Users className="h-4 w-4 text-muted-foreground mr-1" />
                </div>
                <div className="text-sm font-medium">{product.reviewCount}</div>
                <div className="text-xs text-muted-foreground">Đánh giá</div>
              </div>
            </div>
          </div>

          {/* Enhanced Product Info */}
          <div className="space-y-6">
            {/* Title, Brand and Rating */}
            <div>
              <div className="flex items-center gap-2 mb-2">
                {product.brand && (
                  <Badge variant="outline" className="text-xs">
                    {product.brand}
                  </Badge>
                )}
                <Badge variant="outline" className="text-xs">
                  {product.category.name}
                </Badge>
              </div>

              <h1 className="text-3xl lg:text-4xl font-bold mb-3 leading-tight">
                {product.name}
              </h1>

              <div className="flex items-center gap-4 mb-3">
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-1">
                    {renderStars(product.avgRating)}
                  </div>
                  <span className="text-sm font-medium">
                    {product.avgRating.toFixed(1)}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    ({product.reviewCount} đánh giá)
                  </span>
                </div>

                {product.avgRating >= 4.5 && (
                  <Badge className="bg-yellow-500 text-white">
                    <Award className="h-3 w-3 mr-1" />
                    Chất lượng cao
                  </Badge>
                )}
              </div>

              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <span>SKU: {product.sku}</span>
                {product.stock > 0 ? (
                  <span className="text-green-600 font-medium">
                    ✓ Còn hàng ({product.stock} sản phẩm)
                  </span>
                ) : (
                  <span className="text-red-600 font-medium">✗ Hết hàng</span>
                )}
              </div>
            </div>

            {/* Enhanced Price */}
            <div className="space-y-3 p-4 bg-gradient-to-r from-fashion-50 to-fashion-100 dark:from-fashion-900/20 dark:to-fashion-800/20 rounded-xl">
              {product.salePrice ? (
                <>
                  <div className="flex items-center gap-3">
                    <div className="text-3xl lg:text-4xl font-bold text-fashion-600">
                      {formatCurrency(product.salePrice)}
                    </div>
                    <div className="text-lg text-muted-foreground line-through">
                      {formatCurrency(product.price)}
                    </div>
                  </div>
                  <div className="text-sm text-green-600 font-medium">
                    Tiết kiệm{" "}
                    {formatCurrency(product.price - product.salePrice)}(
                    {Math.round(
                      ((product.price - product.salePrice) / product.price) *
                        100
                    )}
                    %)
                  </div>
                </>
              ) : (
                <div className="text-3xl lg:text-4xl font-bold text-fashion-600">
                  {formatCurrency(product.price)}
                </div>
              )}
            </div>

            {/* Quick Description */}
            <div className="prose prose-sm max-w-none">
              <p className="text-muted-foreground leading-relaxed">
                {product.description}
              </p>
            </div>

            {/* Description */}
            <div>
              <h3 className="font-semibold mb-2">Mô tả sản phẩm</h3>
              <p className="text-muted-foreground leading-relaxed">
                {product.description}
              </p>
            </div>

            {/* Tags */}
            {product.tags.length > 0 && (
              <div>
                <h3 className="font-semibold mb-2">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {product.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-gray-100 text-sm rounded"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Enhanced Quantity and Actions */}
            <div className="space-y-6">
              {/* Quantity Selector */}
              <div>
                <label className="block text-sm font-medium mb-3">
                  Số lượng
                </label>
                <div className="flex items-center gap-3">
                  <div className="flex items-center border rounded-lg">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      disabled={quantity <= 1}
                      className="h-10 w-10 p-0 rounded-r-none"
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                    <div className="w-16 h-10 flex items-center justify-center border-x font-medium">
                      {quantity}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        setQuantity(Math.min(product.stock, quantity + 1))
                      }
                      disabled={quantity >= product.stock}
                      className="h-10 w-10 p-0 rounded-l-none"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    Tối đa {product.stock} sản phẩm
                  </span>
                </div>
              </div>

              {/* Main Action Buttons */}
              <div className="space-y-3">
                <div className="flex gap-3">
                  <Button
                    onClick={handleAddToCart}
                    className="flex-1 h-12 bg-fashion-600 hover:bg-fashion-700 text-white font-medium"
                    disabled={product.stock === 0}
                    size="lg"
                  >
                    <ShoppingCart className="h-5 w-5 mr-2" />
                    {product.stock === 0 ? "Hết hàng" : "Thêm vào giỏ hàng"}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleWishlist}
                    className={`h-12 px-4 ${
                      isWishlisted
                        ? "text-fashion-600 border-fashion-600 bg-fashion-50"
                        : ""
                    }`}
                    size="lg"
                  >
                    <Heart
                      className={`h-5 w-5 ${
                        isWishlisted ? "fill-current" : ""
                      }`}
                    />
                  </Button>
                </div>

                {/* Secondary Actions */}
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={toggleCompare}
                    className="flex-1"
                    size="sm"
                  >
                    <GitCompare className="h-4 w-4 mr-2" />
                    {compareList.includes(product.id)
                      ? "Đã thêm so sánh"
                      : "So sánh"}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={copyProductLink}
                    size="sm"
                    className="px-3"
                  >
                    {copiedLink ? (
                      <Check className="h-4 w-4 text-green-600" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => shareToSocial("facebook")}
                    size="sm"
                    className="px-3"
                  >
                    <Facebook className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => shareToSocial("twitter")}
                    size="sm"
                    className="px-3"
                  >
                    <Twitter className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-6 border-t">
              <div className="flex items-center gap-2 text-sm">
                <Truck className="h-4 w-4 text-green-600" />
                <span>Miễn phí vận chuyển</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Shield className="h-4 w-4 text-blue-600" />
                <span>Bảo hành chính hãng</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <RotateCcw className="h-4 w-4 text-orange-600" />
                <span>Đổi trả 30 ngày</span>
              </div>
            </div>
          </div>
        </div>

        {/* Reviews Section */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold">Đánh giá sản phẩm</h2>

          {product.reviews.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-muted-foreground">
                  Chưa có đánh giá nào cho sản phẩm này
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {product.reviews.map((review) => (
                <Card key={review.id}>
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center">
                        {review.user.avatar ? (
                          <ClientImage
                            src={review.user.avatar}
                            alt={review.user.name}
                            width={40}
                            height={40}
                            className="rounded-full"
                            fallbackSrc="/images/placeholder.jpg"
                          />
                        ) : (
                          <span className="text-pink-600 font-medium">
                            {review.user.name.charAt(0).toUpperCase()}
                          </span>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="font-medium">
                            {review.user.name}
                          </span>
                          <div className="flex items-center gap-1">
                            {renderStars(review.rating)}
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {new Date(review.createdAt).toLocaleDateString(
                              "vi-VN"
                            )}
                          </span>
                        </div>
                        {review.comment && (
                          <p className="text-muted-foreground mb-2">
                            {review.comment}
                          </p>
                        )}
                        {review.images.length > 0 && (
                          <div className="flex gap-2">
                            {review.images.map((image, index) => (
                              <div
                                key={index}
                                className="relative w-16 h-16 rounded overflow-hidden"
                              >
                                <ClientImage
                                  src={image}
                                  alt={`Review image ${index + 1}`}
                                  fill
                                  className="object-cover"
                                  fallbackSrc="/images/placeholder.jpg"
                                />
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Related Products */}
        {relatedProducts.length > 0 && (
          <div className="container mx-auto px-4 py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-8">
              Sản phẩm liên quan
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {relatedProducts.map((relatedProduct) => (
                <Card
                  key={relatedProduct.id}
                  className="group hover:shadow-lg transition-shadow"
                >
                  <CardContent className="p-0">
                    <Link href={`/products/${relatedProduct.slug}`}>
                      <div className="relative aspect-square overflow-hidden rounded-t-lg">
                        <ClientImage
                          src={
                            relatedProduct.images?.[0] ||
                            "/images/placeholder.jpg"
                          }
                          alt={relatedProduct.name}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                          fallbackSrc="/images/placeholder.jpg"
                        />
                        {relatedProduct.salePrice && (
                          <Badge className="absolute top-2 left-2 bg-red-500 text-white">
                            -
                            {Math.round(
                              ((relatedProduct.price -
                                relatedProduct.salePrice) /
                                relatedProduct.price) *
                                100
                            )}
                            %
                          </Badge>
                        )}
                      </div>
                    </Link>
                    <div className="p-4">
                      <Link href={`/products/${relatedProduct.slug}`}>
                        <h3 className="font-medium text-gray-900 mb-2 line-clamp-2 hover:text-fashion-600 transition-colors">
                          {relatedProduct.name}
                        </h3>
                      </Link>
                      <div className="flex items-center gap-2 mb-2">
                        <div className="flex items-center">
                          {Array.from({ length: 5 }, (_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${
                                i < Math.floor(relatedProduct.avgRating || 0)
                                  ? "text-yellow-400 fill-current"
                                  : "text-gray-300"
                              }`}
                            />
                          ))}
                        </div>
                        <span className="text-sm text-gray-500">
                          ({relatedProduct.reviewCount || 0})
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {relatedProduct.salePrice ? (
                            <>
                              <span className="text-lg font-bold text-fashion-600">
                                {formatCurrency(relatedProduct.salePrice)}
                              </span>
                              <span className="text-sm text-gray-500 line-through">
                                {formatCurrency(relatedProduct.price)}
                              </span>
                            </>
                          ) : (
                            <span className="text-lg font-bold text-gray-900">
                              {formatCurrency(relatedProduct.price)}
                            </span>
                          )}
                        </div>
                        <Button
                          size="sm"
                          onClick={async (e) => {
                            e.preventDefault();
                            try {
                              await quickAdd(relatedProduct.id, 1);
                              toast.success("Đã thêm vào giỏ hàng");
                            } catch {
                              toast.error("Có lỗi xảy ra");
                            }
                          }}
                          disabled={quickAddLoading}
                          className="h-8 w-8 p-0"
                        >
                          <ShoppingCart className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
}
