import { useState, useEffect } from "react";

interface SiteSettings {
  siteName: string;
  siteDescription: string;
  siteUrl: string;
  logo: string;
  favicon: string;
  contactEmail: string;
  contactPhone: string;
  address: string;
  // Enhanced structure for better component integration
  siteInfo: {
    name: string;
    description: string;
    url: string;
    logo: string;
    favicon: string;
  };
  contactInfo: {
    email: string;
    phone: string;
    address: string;
  };
  socialMedia: {
    facebook: string;
    instagram: string;
    twitter: string;
    youtube?: string;
  };
  paymentMethods: {
    cod: boolean;
    bankTransfer: boolean;
    creditCard: boolean;
  };
  shippingSettings: {
    freeShippingThreshold: number;
    shippingFee: number;
    estimatedDelivery: string;
  };
  emailSettings: {
    smtpHost: string;
    smtpPort: number;
    smtpUser: string;
    smtpPassword: string;
    fromEmail: string;
    fromName: string;
  };
  notifications: {
    orderNotifications: boolean;
    stockAlerts: boolean;
    customerNotifications: boolean;
  };
  seoSettings?: {
    metaTitle: string;
    metaDescription: string;
    metaKeywords: string;
    googleAnalytics: string;
    facebookPixel: string;
  };
  securitySettings?: {
    enableTwoFactor: boolean;
    sessionTimeout: number;
    maxLoginAttempts: number;
    enableCaptcha: boolean;
  };
}

const DEFAULT_SETTINGS: SiteSettings = {
  siteName: "NS Shop",
  siteDescription: "Cửa hàng thời trang trực tuyến",
  siteUrl: "https://nsshop.com",
  logo: "",
  favicon: "",
  contactEmail: "<EMAIL>",
  contactPhone: "**********",
  address: "123 Đường ABC, Quận 1, TP.HCM",
  siteInfo: {
    name: "NS Shop",
    description:
      "Khám phá xu hướng thời trang mới nhất và phong cách độc đáo tại NS Shop. Chúng tôi mang đến cho bạn những sản phẩm chất lượng cao với giá cả hợp lý.",
    url: "https://nsshop.com",
    logo: "",
    favicon: "",
  },
  contactInfo: {
    email: "<EMAIL>",
    phone: "+84 123 456 789",
    address: "123 Đường ABC, Quận 1, TP.HCM",
  },
  socialMedia: {
    facebook: "https://facebook.com/nsshop",
    instagram: "https://instagram.com/nsshop",
    twitter: "https://twitter.com/nsshop",
    youtube: "https://youtube.com/nsshop",
  },
  paymentMethods: {
    cod: true,
    bankTransfer: true,
    creditCard: false,
  },
  shippingSettings: {
    freeShippingThreshold: 500000,
    shippingFee: 30000,
    estimatedDelivery: "2-3 ngày",
  },
  emailSettings: {
    smtpHost: "",
    smtpPort: 587,
    smtpUser: "",
    smtpPassword: "",
    fromEmail: "<EMAIL>",
    fromName: "NS Shop",
  },
  notifications: {
    orderNotifications: true,
    stockAlerts: true,
    customerNotifications: true,
  },
  seoSettings: {
    metaTitle: "NS Shop - Thời trang trực tuyến",
    metaDescription:
      "Cửa hàng thời trang trực tuyến với những sản phẩm chất lượng cao",
    metaKeywords: "thời trang, quần áo, giày dép, phụ kiện",
    googleAnalytics: "",
    facebookPixel: "",
  },
  securitySettings: {
    enableTwoFactor: false,
    sessionTimeout: 24,
    maxLoginAttempts: 5,
    enableCaptcha: false,
  },
};

export function useSettings() {
  const [settings, setSettings] = useState<SiteSettings>(DEFAULT_SETTINGS);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch("/api/settings");

      if (!response.ok) {
        throw new Error("Failed to fetch settings");
      }

      const data = await response.json();
      setSettings({ ...DEFAULT_SETTINGS, ...data });
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unknown error");
      console.error("Error fetching settings:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const getSetting = (key: keyof SiteSettings) => {
    return settings[key];
  };

  const getNestedSetting = (section: string, key: string) => {
    const sectionData = settings[section as keyof SiteSettings] as any;
    return sectionData?.[key];
  };

  return {
    settings,
    loading,
    error,
    fetchSettings,
    getSetting,
    getNestedSetting,
  };
}

export type { SiteSettings };
