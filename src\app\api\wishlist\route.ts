import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/route";
import { z } from "zod";

const addToWishlistSchema = z.object({
  productId: z.string().min(1, "Product ID là bắt buộc"),
});

// GET /api/wishlist - L<PERSON>y danh sách yêu thích của user
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user || !session.user.id) {
      return NextResponse.json(
        { error: "Vui lòng đăng nhập" },
        { status: 401 }
      );
    }

    const wishlistItems = await prisma.wishlistItem.findMany({
      where: { userId: session.user.id },
      include: {
        product: {
          include: {
            category: {
              select: {
                name: true,
              },
            },
            reviews: {
              select: {
                rating: true,
              },
            },
            media: {
              include: {
                media: {
                  select: {
                    id: true,
                    url: true,
                    alt: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Calculate average rating for each product
    const wishlistWithRatings = wishlistItems.map((item) => {
      const reviews = item.product.reviews;
      const avgRating =
        reviews.length > 0
          ? reviews.reduce((sum, review) => sum + review.rating, 0) /
            reviews.length
          : 0;

      return {
        id: item.id,
        product: {
          id: item.product.id,
          name: item.product.name,
          slug: item.product.slug,
          price: item.product.price,
          salePrice: item.product.salePrice,
          images: item.product.media?.map((m) => m.media.url) || [],
          avgRating: Math.round(avgRating * 10) / 10,
          reviewCount: reviews.length,
          stock: item.product.stock,
          category: {
            name: item.product.category.name,
          },
        },
        createdAt: item.createdAt,
      };
    });

    return NextResponse.json(wishlistWithRatings);
  } catch (error) {
    console.error("Get wishlist error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy danh sách yêu thích" },
      { status: 500 }
    );
  }
}

// POST /api/wishlist - Thêm sản phẩm vào danh sách yêu thích
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user || !session.user.id) {
      return NextResponse.json(
        { error: "Vui lòng đăng nhập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { productId } = addToWishlistSchema.parse(body);

    // Kiểm tra sản phẩm có tồn tại không
    const product = await prisma.product.findUnique({
      where: { id: productId },
    });

    if (!product) {
      return NextResponse.json(
        { error: "Sản phẩm không tồn tại" },
        { status: 404 }
      );
    }

    // Kiểm tra xem sản phẩm đã có trong wishlist chưa
    const existingItem = await prisma.wishlistItem.findUnique({
      where: {
        userId_productId: {
          userId: session.user.id,
          productId: productId,
        },
      },
    });

    if (existingItem) {
      return NextResponse.json(
        { error: "Sản phẩm đã có trong danh sách yêu thích" },
        { status: 400 }
      );
    }

    // Thêm vào wishlist
    const wishlistItem = await prisma.wishlistItem.create({
      data: {
        userId: session.user.id,
        productId: productId,
      },
      include: {
        product: {
          include: {
            category: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({
      message: "Đã thêm vào danh sách yêu thích",
      item: wishlistItem,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Add to wishlist error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi thêm vào danh sách yêu thích" },
      { status: 500 }
    );
  }
}
