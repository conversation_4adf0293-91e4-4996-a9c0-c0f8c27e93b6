import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// GET /api/products/related?productId=xxx - L<PERSON>y sản phẩm liên quan
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get("productId");
    const limit = parseInt(searchParams.get("limit") || "4");

    if (!productId) {
      return NextResponse.json({ error: "productId là bắt buộc" }, { status: 400 });
    }

    // Get the current product to find related products
    const currentProduct = await prisma.product.findUnique({
      where: { id: productId, status: "ACTIVE" },
      select: {
        categoryId: true,
        brandId: true,
        tags: true,
        price: true,
      },
    });

    if (!currentProduct) {
      return NextResponse.json(
        { error: "<PERSON>hông tìm thấy sản phẩm" },
        { status: 404 }
      );
    }

    // Find related products based on:
    // 1. Same category (highest priority)
    // 2. Same brand
    // 3. Similar price range (±30%)
    // 4. Similar tags
    const priceMin = currentProduct.price * 0.7;
    const priceMax = currentProduct.price * 1.3;

    const relatedProducts = await prisma.product.findMany({
      where: {
        AND: [
          { id: { not: productId } }, // Exclude current product
          { status: "ACTIVE" },
          {
            OR: [
              // Same category (highest priority)
              { categoryId: currentProduct.categoryId },
              // Same brand
              { brandId: currentProduct.brandId },
              // Similar price range
              {
                price: {
                  gte: priceMin,
                  lte: priceMax,
                },
              },
              // Similar tags
              {
                tags: {
                  hasSome: currentProduct.tags,
                },
              },
            ],
          },
        ],
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        reviews: {
          select: {
            rating: true,
          },
        },
        _count: {
          select: {
            reviews: true,
          },
        },
      },
      orderBy: [
        // Prioritize same category
        {
          categoryId: currentProduct.categoryId ? "asc" : "desc",
        },
        // Then by rating
        {
          avgRating: "desc",
        },
        // Then by creation date
        {
          createdAt: "desc",
        },
      ],
      take: limit,
    });

    // Calculate average rating for each product
    const productsWithRating = relatedProducts.map((product) => {
      const avgRating =
        product.reviews.length > 0
          ? product.reviews.reduce((sum, review) => sum + review.rating, 0) /
            product.reviews.length
          : 0;

      return {
        ...product,
        avgRating: Math.round(avgRating * 10) / 10,
        reviewCount: product._count.reviews,
        reviews: undefined, // Remove reviews from response
      };
    });

    return NextResponse.json({
      data: productsWithRating,
      total: productsWithRating.length,
    });
  } catch (error) {
    console.error("Get related products error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy sản phẩm liên quan" },
      { status: 500 }
    );
  }
}
