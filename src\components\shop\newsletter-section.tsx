"use client";

import { useState } from "react";
import {
  <PERSON>,
  Gift,
  Sparkles,
  <PERSON>,
  Check,
  ArrowRight,
  Zap,
  Crown,
  Heart,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export function NewsletterSection() {
  const [email, setEmail] = useState("");
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1500));

    setIsSubscribed(true);
    setIsLoading(false);
    setEmail("");

    // Reset after 5 seconds
    setTimeout(() => {
      setIsSubscribed(false);
    }, 5000);
  };

  return (
    <section className="py-20 lg:py-32 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-fashion-50 via-white to-fashion-50" />
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-fashion-200/30 rounded-full blur-3xl" />
      <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-yellow-200/40 rounded-full blur-3xl" />

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Main Newsletter Card */}
          <div className="bg-white rounded-3xl shadow-2xl overflow-hidden border border-gray-100">
            <div className="grid grid-cols-1 lg:grid-cols-2">
              {/* Left Side - Visual Content */}
              <div className="bg-gradient-to-br from-fashion-600 via-fashion-700 to-fashion-800 p-8 lg:p-12 text-white relative overflow-hidden">
                {/* Decorative Elements */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full blur-2xl" />
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-yellow-300/20 rounded-full blur-xl" />
                <div className="absolute top-1/2 left-1/2 w-40 h-40 bg-white/5 rounded-full blur-3xl transform -translate-x-1/2 -translate-y-1/2" />

                {/* Floating Icons */}
                <div className="absolute top-8 right-8 animate-bounce">
                  <div className="w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center">
                    <Crown className="w-4 h-4 text-yellow-800" />
                  </div>
                </div>
                <div className="absolute bottom-12 right-12 animate-pulse">
                  <div className="w-6 h-6 bg-pink-400 rounded-full flex items-center justify-center">
                    <Heart className="w-3 h-3 text-pink-800" />
                  </div>
                </div>
                <div className="absolute top-1/3 left-8 animate-ping">
                  <div className="w-4 h-4 bg-white/30 rounded-full" />
                </div>

                <div className="relative z-10 h-full flex flex-col justify-center">
                  {/* Header Badge */}
                  <div className="flex items-center gap-3 mb-6">
                    <div className="p-3 bg-white/20 rounded-2xl backdrop-blur-sm">
                      <Sparkles className="h-6 w-6 text-yellow-300" />
                    </div>
                    <Badge className="bg-yellow-400 text-yellow-900 hover:bg-yellow-300 px-4 py-2 text-sm font-semibold">
                      <Zap className="w-3 h-3 mr-1" />
                      Ưu đãi VIP
                    </Badge>
                  </div>

                  {/* Main Content */}
                  <div className="space-y-6">
                    <h2 className="text-4xl lg:text-5xl font-bold leading-tight">
                      Tham gia
                      <span className="block bg-gradient-to-r from-yellow-300 to-yellow-400 bg-clip-text text-transparent">
                        NS Club
                      </span>
                      <span className="text-2xl lg:text-3xl font-medium block mt-2">
                        Nhận ngay 100K
                      </span>
                    </h2>

                    <p className="text-xl text-white/90 leading-relaxed">
                      Trở thành thành viên VIP và tận hưởng những đặc quyền độc
                      quyền chỉ dành cho bạn
                    </p>
                  </div>

                  {/* Benefits List */}
                  <div className="mt-8 space-y-4">
                    {[
                      { icon: Star, text: "Ưu đãi độc quyền lên đến 50%" },
                      { icon: Zap, text: "Thông tin sản phẩm mới sớm nhất" },
                      {
                        icon: Crown,
                        text: "Tư vấn phong cách cá nhân miễn phí",
                      },
                    ].map((benefit, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-4 group"
                      >
                        <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center group-hover:bg-white/30 transition-colors">
                          <benefit.icon className="w-5 h-5 text-yellow-300" />
                        </div>
                        <span className="text-white/90 font-medium">
                          {benefit.text}
                        </span>
                      </div>
                    ))}
                  </div>

                  {/* Stats */}
                  <div className="mt-8 pt-6 border-t border-white/20">
                    <div className="grid grid-cols-2 gap-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-yellow-300">
                          50K+
                        </div>
                        <div className="text-sm text-white/70">
                          Thành viên VIP
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-yellow-300">
                          4.9★
                        </div>
                        <div className="text-sm text-white/70">
                          Đánh giá dịch vụ
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Side - Form */}
              <div className="p-8 lg:p-12 bg-white">
                <div className="h-full flex flex-col justify-center">
                  {isSubscribed ? (
                    /* Success State */
                    <div className="text-center space-y-6 animate-in fade-in duration-500">
                      <div className="relative">
                        <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto shadow-lg">
                          <Check className="h-10 w-10 text-white" />
                        </div>
                        <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center animate-bounce">
                          <Sparkles className="w-4 h-4 text-yellow-800" />
                        </div>
                      </div>

                      <div className="space-y-3">
                        <h3 className="text-2xl font-bold text-gray-900">
                          Chào mừng bạn đến NS Club! 🎉
                        </h3>
                        <p className="text-gray-600 leading-relaxed">
                          Voucher 100.000đ đã được gửi đến email của bạn.
                          <br />
                          Hãy kiểm tra hộp thư và bắt đầu mua sắm ngay!
                        </p>
                      </div>

                      <div className="bg-gradient-to-r from-fashion-50 to-yellow-50 rounded-2xl p-6 border border-fashion-200">
                        <div className="flex items-center justify-center gap-3 text-fashion-700">
                          <Gift className="w-5 h-5" />
                          <span className="font-semibold">
                            Mã voucher: WELCOME100
                          </span>
                        </div>
                      </div>
                    </div>
                  ) : (
                    /* Form State */
                    <div className="space-y-8">
                      {/* Form Header */}
                      <div className="text-center space-y-4">
                        <div className="inline-flex items-center gap-2 bg-fashion-100 text-fashion-700 px-4 py-2 rounded-full text-sm font-medium">
                          <Crown className="w-4 h-4" />
                          Tham gia miễn phí
                        </div>
                        <h3 className="text-2xl lg:text-3xl font-bold text-gray-900">
                          Đăng ký ngay để nhận
                          <span className="block text-fashion-600">
                            100.000đ voucher
                          </span>
                        </h3>
                        <p className="text-gray-600">
                          Chỉ cần 30 giây để trở thành thành viên VIP
                        </p>
                      </div>

                      {/* Form */}
                      <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="space-y-3">
                          <label
                            htmlFor="newsletter-email"
                            className="block text-sm font-semibold text-gray-700"
                          >
                            Địa chỉ email
                          </label>
                          <div className="relative group">
                            <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 group-focus-within:text-fashion-500 transition-colors" />
                            <input
                              id="newsletter-email"
                              type="email"
                              value={email}
                              onChange={(e) => setEmail(e.target.value)}
                              placeholder="<EMAIL>"
                              className="w-full pl-12 pr-4 py-4 bg-gray-50 border-2 border-gray-200 rounded-2xl text-gray-900 placeholder-gray-400 focus:outline-none focus:border-fashion-500 focus:bg-white transition-all duration-200"
                              required
                              disabled={isLoading}
                            />
                          </div>
                        </div>

                        <Button
                          type="submit"
                          disabled={isLoading}
                          className="w-full bg-gradient-to-r from-fashion-600 to-fashion-700 hover:from-fashion-700 hover:to-fashion-800 text-white font-semibold py-4 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-200 group"
                          size="lg"
                        >
                          {isLoading ? (
                            <div className="flex items-center gap-3">
                              <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                              Đang xử lý...
                            </div>
                          ) : (
                            <div className="flex items-center gap-3">
                              <span>Nhận voucher 100K ngay</span>
                              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                            </div>
                          )}
                        </Button>
                      </form>

                      {/* Trust Indicators */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-center gap-6 text-sm text-gray-500">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full" />
                            <span>Miễn phí 100%</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full" />
                            <span>Không spam</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full" />
                            <span>Hủy bất kỳ lúc nào</span>
                          </div>
                        </div>

                        <p className="text-xs text-gray-500 text-center leading-relaxed">
                          Bằng cách đăng ký, bạn đồng ý với{" "}
                          <a
                            href="/privacy"
                            className="text-fashion-600 hover:text-fashion-700 underline"
                          >
                            Chính sách bảo mật
                          </a>{" "}
                          và{" "}
                          <a
                            href="/terms"
                            className="text-fashion-600 hover:text-fashion-700 underline"
                          >
                            Điều khoản sử dụng
                          </a>{" "}
                          của chúng tôi.
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Stats */}
          <div className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-6">
            {[
              { number: "50K+", label: "Thành viên VIP", icon: Crown },
              { number: "100K", label: "Voucher tặng", icon: Gift },
              { number: "24/7", label: "Hỗ trợ khách hàng", icon: Heart },
              { number: "4.9★", label: "Đánh giá dịch vụ", icon: Star },
            ].map((stat, index) => (
              <div key={index} className="text-center group">
                <div className="w-12 h-12 bg-white rounded-2xl shadow-lg flex items-center justify-center mx-auto mb-3 group-hover:shadow-xl transition-shadow">
                  <stat.icon className="w-6 h-6 text-fashion-600" />
                </div>
                <div className="text-2xl font-bold text-gray-900">
                  {stat.number}
                </div>
                <div className="text-sm text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
