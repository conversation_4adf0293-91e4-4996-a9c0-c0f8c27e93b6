import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";
import { seedEmailTemplates } from "./email-templates-seed";

const prisma = new PrismaClient();

async function seedBasicData() {
  console.log("🌱 Seeding basic data...");

  // Tạo admin user
  const adminPassword = await bcrypt.hash("admin123", 12);
  const admin = await prisma.adminUser.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      name: "NS Shop Admin",
      password: adminPassword,
      role: "ADMIN",
      isActive: true,
    },
  });

  // Tạo moderator user
  const moderatorPassword = await bcrypt.hash("moderator123", 12);
  const moderator = await prisma.adminUser.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      name: "NS Shop Moderator",
      password: moderatorPassword,
      role: "MODERATOR",
      isActive: true,
      permissions: {
        manage_products: true,
        manage_orders: true,
        manage_categories: true,
        view_analytics: true,
        manage_users: true,
      },
      createdBy: admin.id,
    },
  });

  return { admin, moderator };
}

async function seedMedia() {
  console.log("🌱 Seeding media...");

  const mediaItems = [];

  // Brand logos
  const brandLogos = [
    {
      filename: "nike-logo.png",
      path: "brands/nike-logo.png",
      url: "https://picsum.photos/200/100?random=101",
      mimeType: "image/png",
      size: 15000,
      width: 200,
      height: 100,
      alt: "Nike Logo",
      title: "Nike Brand Logo",
      folder: "brands",
      type: "EXTERNAL",
      externalUrl: "https://picsum.photos/200/100?random=101",
    },
    {
      filename: "adidas-logo.png",
      path: "brands/adidas-logo.png",
      url: "https://picsum.photos/200/100?random=102",
      mimeType: "image/png",
      size: 16000,
      width: 200,
      height: 100,
      alt: "Adidas Logo",
      title: "Adidas Brand Logo",
      folder: "brands",
      type: "EXTERNAL",
      externalUrl: "https://picsum.photos/200/100?random=102",
    },
    {
      filename: "zara-logo.png",
      path: "brands/zara-logo.png",
      url: "https://picsum.photos/200/100?random=103",
      mimeType: "image/png",
      size: 14000,
      width: 200,
      height: 100,
      alt: "Zara Logo",
      title: "Zara Brand Logo",
      folder: "brands",
      type: "EXTERNAL",
      externalUrl: "https://picsum.photos/200/100?random=103",
    },
    {
      filename: "hm-logo.png",
      path: "brands/hm-logo.png",
      url: "https://picsum.photos/200/100?random=104",
      mimeType: "image/png",
      size: 13000,
      width: 200,
      height: 100,
      alt: "H&M Logo",
      title: "H&M Brand Logo",
      folder: "brands",
      type: "EXTERNAL",
      externalUrl: "https://picsum.photos/200/100?random=104",
    },
    {
      filename: "uniqlo-logo.png",
      path: "brands/uniqlo-logo.png",
      url: "https://picsum.photos/200/100?random=105",
      mimeType: "image/png",
      size: 17000,
      width: 200,
      height: 100,
      alt: "Uniqlo Logo",
      title: "Uniqlo Brand Logo",
      folder: "brands",
      type: "EXTERNAL",
      externalUrl: "https://picsum.photos/200/100?random=105",
    },
  ];

  // Product images
  for (let i = 1; i <= 150; i++) {
    mediaItems.push({
      filename: `product-${i}.jpg`,
      path: `products/product-${i}.jpg`,
      url: `https://picsum.photos/500/500?random=${i + 1000}`,
      mimeType: "image/jpeg",
      size: Math.floor(Math.random() * 100000) + 50000,
      width: 500,
      height: 500,
      alt: `Product Image ${i}`,
      title: `Product Image ${i}`,
      folder: "products",
      type: "EXTERNAL",
      externalUrl: `https://picsum.photos/500/500?random=${i + 1000}`,
    });
  }

  // Post featured images
  for (let i = 1; i <= 20; i++) {
    mediaItems.push({
      filename: `post-${i}.jpg`,
      path: `posts/post-${i}.jpg`,
      url: `https://picsum.photos/800/600?random=${i + 4000}`,
      mimeType: "image/jpeg",
      size: Math.floor(Math.random() * 150000) + 80000,
      width: 800,
      height: 600,
      alt: `Post Featured Image ${i}`,
      title: `Post Featured Image ${i}`,
      folder: "posts",
      type: "EXTERNAL",
      externalUrl: `https://picsum.photos/800/600?random=${i + 4000}`,
    });
  }

  // Page featured images
  for (let i = 1; i <= 5; i++) {
    mediaItems.push({
      filename: `page-${i}.jpg`,
      path: `pages/page-${i}.jpg`,
      url: `https://picsum.photos/800/600?random=${i + 5000}`,
      mimeType: "image/jpeg",
      size: Math.floor(Math.random() * 150000) + 80000,
      width: 800,
      height: 600,
      alt: `Page Featured Image ${i}`,
      title: `Page Featured Image ${i}`,
      folder: "pages",
      type: "EXTERNAL",
      externalUrl: `https://picsum.photos/800/600?random=${i + 5000}`,
    });
  }

  // Event images
  for (let i = 1; i <= 5; i++) {
    mediaItems.push({
      filename: `event-${i}.jpg`,
      path: `events/event-${i}.jpg`,
      url: `https://picsum.photos/800/400?random=${i + 1000}`,
      mimeType: "image/jpeg",
      size: Math.floor(Math.random() * 120000) + 70000,
      width: 800,
      height: 400,
      alt: `Event Image ${i}`,
      title: `Event Image ${i}`,
      folder: "events",
      type: "EXTERNAL",
      externalUrl: `https://picsum.photos/800/400?random=${i + 1000}`,
    });
  }

  // Create all media items
  const allMediaItems = [...brandLogos, ...mediaItems];
  const createdMedia = [];

  for (const mediaData of allMediaItems) {
    const media = await prisma.media.create({
      data: {
        ...mediaData,
        type: mediaData.type as any, // Cast to MediaType enum
      },
    });
    createdMedia.push(media);
  }

  console.log(`✅ Media seeded successfully: ${createdMedia.length} items`);
  return createdMedia;
}

async function seedBrands(mediaItems: any[]) {
  console.log("🌱 Seeding brands...");

  const brandLogos = mediaItems.filter((m) => m.folder === "brands");

  const brands = [
    {
      name: "Nike",
      slug: "nike",
      description:
        "Thương hiệu thể thao hàng đầu thế giới với slogan Just Do It",
      website: "https://www.nike.com",
      logoId: brandLogos[0]?.id,
      isActive: true,
    },
    {
      name: "Adidas",
      slug: "adidas",
      description: "Thương hiệu thể thao Đức nổi tiếng với 3 sọc đặc trưng",
      website: "https://www.adidas.com",
      logoId: brandLogos[1]?.id,
      isActive: true,
    },
    {
      name: "Zara",
      slug: "zara",
      description: "Thương hiệu thời trang nhanh hàng đầu từ Tây Ban Nha",
      website: "https://www.zara.com",
      logoId: brandLogos[2]?.id,
      isActive: true,
    },
    {
      name: "H&M",
      slug: "hm",
      description: "Thương hiệu thời trang Thụy Điển với phong cách trẻ trung",
      website: "https://www.hm.com",
      logoId: brandLogos[3]?.id,
      isActive: true,
    },
    {
      name: "Uniqlo",
      slug: "uniqlo",
      description: "Thương hiệu thời trang Nhật Bản với chất lượng cao",
      website: "https://www.uniqlo.com",
      logoId: brandLogos[4]?.id,
      isActive: true,
    },
  ];

  const createdBrands = [];
  for (const brandData of brands) {
    const brand = await prisma.brand.upsert({
      where: { slug: brandData.slug },
      update: {},
      create: brandData,
    });
    createdBrands.push(brand);
  }

  console.log("✅ Brands seeded successfully");
  return createdBrands;
}

async function seedUsers() {
  console.log("🌱 Seeding users...");

  const users = [];

  // Tạo 10 user accounts
  for (let i = 1; i <= 10; i++) {
    const userPassword = await bcrypt.hash(`user${i}123`, 12);
    const user = await prisma.user.upsert({
      where: { email: `user${i}@nsshop.com` },
      update: {},
      create: {
        email: `user${i}@nsshop.com`,
        name: `Người dùng ${i}`,
        password: userPassword,
        phone: `*********${i}`,
        gender: i % 2 === 0 ? "FEMALE" : "MALE",
      },
    });
    users.push(user);
  }

  return users;
}

async function seedSettings() {
  console.log("🌱 Seeding settings...");

  const settings = [
    { key: "siteName", value: "NS Shop", type: "string" },
    {
      key: "siteDescription",
      value: "Cửa hàng thời trang trực tuyến",
      type: "string",
    },
    { key: "contactEmail", value: "<EMAIL>", type: "string" },
    { key: "contactPhone", value: "*********9", type: "string" },
    { key: "address", value: "123 Đường ABC, Quận 1, TP.HCM", type: "string" },
    { key: "currency", value: "VND", type: "string" },
    { key: "shippingFee", value: 30000, type: "number" },
    { key: "freeShippingThreshold", value: 500000, type: "number" },
  ];

  for (const setting of settings) {
    await prisma.setting.upsert({
      where: { key: setting.key },
      update: { value: setting.value },
      create: setting,
    });
  }
}

async function seedCategories() {
  console.log("🌱 Seeding categories...");

  const categories = [
    {
      name: "Thời trang nữ",
      slug: "thoi-trang-nu",
      description: "Quần áo, phụ kiện thời trang dành cho nữ",
      children: [
        { name: "Áo", slug: "ao-nu" },
        { name: "Quần", slug: "quan-nu" },
        { name: "Váy", slug: "vay" },
        { name: "Đầm", slug: "dam" },
      ],
    },
    {
      name: "Thời trang nam",
      slug: "thoi-trang-nam",
      description: "Quần áo, phụ kiện thời trang dành cho nam",
      children: [
        { name: "Áo", slug: "ao-nam" },
        { name: "Quần", slug: "quan-nam" },
      ],
    },
    {
      name: "Giày dép",
      slug: "giay-dep",
      description: "Giày, dép thời trang",
      children: [
        { name: "Giày nữ", slug: "giay-nu" },
        { name: "Giày nam", slug: "giay-nam" },
      ],
    },
  ];

  for (const category of categories) {
    const parentCategory = await prisma.category.upsert({
      where: { slug: category.slug },
      update: {},
      create: {
        name: category.name,
        slug: category.slug,
        description: category.description,
      },
    });

    // Tạo subcategories
    for (const child of category.children) {
      await prisma.category.upsert({
        where: { slug: child.slug },
        update: {},
        create: {
          name: child.name,
          slug: child.slug,
          parentId: parentCategory.id,
        },
      });
    }
  }
}

async function seedSampleProducts(mediaItems: any[]) {
  console.log("🌱 Seeding 50 sample products...");

  // Lấy categories và brands
  const categories = await prisma.category.findMany({
    where: { parentId: { not: null } }, // Chỉ lấy subcategories
  });

  const brands = await prisma.brand.findMany();

  if (categories.length === 0) {
    throw new Error("Categories not found. Please run seedCategories first.");
  }

  if (brands.length === 0) {
    throw new Error("Brands not found. Please run seedBrands first.");
  }

  // Lấy product images từ media
  const productImages = mediaItems.filter((m) => m.folder === "products");

  const productTemplates = [
    {
      names: ["Áo Blouse", "Áo Sơ Mi", "Áo Thun", "Áo Khoác", "Áo Len"],
      descriptions: [
        "Chất liệu voan mềm mại, thiết kế sang trọng",
        "Cotton cao cấp, thoáng mát",
        "Form dáng ôm, tôn dáng",
        "Phong cách trẻ trung, năng động",
        "Ấm áp, phù hợp mùa đông",
      ],
      priceRange: [200000, 800000],
    },
    {
      names: ["Quần Jeans", "Quần Tây", "Quần Short", "Chân Váy", "Đầm Maxi"],
      descriptions: [
        "Chất liệu jeans co giãn thoải mái",
        "Thiết kế công sở chuyên nghiệp",
        "Phù hợp hoạt động thể thao",
        "Điệu đà, nữ tính",
        "Dáng dài sang trọng",
      ],
      priceRange: [300000, 1200000],
    },
    {
      names: ["Giày Sneaker", "Giày Cao Gót", "Giày Boot", "Sandal", "Dép"],
      descriptions: [
        "Đế êm, phù hợp đi bộ",
        "Thiết kế thanh lịch",
        "Phong cách cá tính",
        "Thoáng khí, thoải mái",
        "Chất liệu cao su bền bỉ",
      ],
      priceRange: [400000, 2000000],
    },
  ];

  const styles = ["Vintage", "Modern", "Classic", "Trendy", "Minimalist"];
  const colors = ["Đen", "Trắng", "Xám", "Hồng", "Xanh", "Đỏ", "Vàng", "Nâu"];

  for (let i = 1; i <= 50; i++) {
    const template = productTemplates[i % productTemplates.length];
    const baseName = template.names[i % template.names.length];
    const style = styles[i % styles.length];
    const color = colors[i % colors.length];
    const description = template.descriptions[i % template.descriptions.length];

    const name = `${baseName} ${style} ${color}`;
    const slug = name
      .toLowerCase()
      .replace(/\s+/g, "-")
      .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
      .replace(/[èéẹẻẽêềếệểễ]/g, "e")
      .replace(/[ìíịỉĩ]/g, "i")
      .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
      .replace(/[ùúụủũưừứựửữ]/g, "u")
      .replace(/[ỳýỵỷỹ]/g, "y")
      .replace(/[đ]/g, "d")
      .replace(/[^a-z0-9-]/g, "");

    const price = Math.floor(
      Math.random() * (template.priceRange[1] - template.priceRange[0]) +
        template.priceRange[0]
    );
    const salePrice = Math.random() > 0.6 ? Math.floor(price * 0.8) : null;
    const stock = Math.floor(Math.random() * 100) + 10;
    const categoryId = categories[i % categories.length].id;
    const brandId = brands[i % brands.length].id;

    const product = await prisma.product.upsert({
      where: { slug: slug },
      update: {},
      create: {
        name,
        slug,
        description: `${description}. ${name} với thiết kế hiện đại, chất lượng cao.`,
        price,
        salePrice,
        sku: `SP${String(i).padStart(3, "0")}`,
        stock,
        categoryId,
        brandId,
        featured: i <= 10, // 10 sản phẩm đầu là featured
        tags: [style.toLowerCase(), color.toLowerCase()],
      },
    });

    // Tạo ProductMedia relationships
    const numImages = Math.floor(Math.random() * 3) + 1; // 1-3 images per product
    for (let j = 0; j < numImages; j++) {
      const mediaIndex = ((i - 1) * 3 + j) % productImages.length;
      const media = productImages[mediaIndex];

      if (media) {
        await prisma.productMedia.create({
          data: {
            productId: product.id,
            mediaId: media.id,
            order: j,
            isPrimary: j === 0, // First image is primary
          },
        });
      }
    }
  }
}

async function seedInventory() {
  console.log("🌱 Seeding inventory entries...");

  // Lấy tất cả products
  const products = await prisma.product.findMany();

  if (products.length === 0) {
    console.log("⚠️ No products found. Skipping inventory seeding.");
    return;
  }

  const locations = [
    "Kho A - Kệ 1",
    "Kho A - Kệ 2",
    "Kho A - Kệ 3",
    "Kho B - Kệ 1",
    "Kho B - Kệ 2",
    "Kho C - Kệ 1",
  ];

  for (const product of products) {
    // Random quantity từ 50-200
    const quantity = Math.floor(Math.random() * 150) + 50;
    // Random reserved từ 0-20
    const reserved = Math.floor(Math.random() * 21);
    const available = quantity - reserved;
    const location = locations[Math.floor(Math.random() * locations.length)];

    const inventoryEntry = await prisma.inventoryEntry.upsert({
      where: { productId: product.id },
      update: {},
      create: {
        productId: product.id,
        quantity,
        available,
        reserved,
        minStock: Math.floor(Math.random() * 10) + 5, // Random minStock 5-15
        maxStock: quantity + Math.floor(Math.random() * 50) + 50, // maxStock higher than current quantity
        location,
      },
    });

    // Tạo stock movement cho việc nhập kho ban đầu
    await prisma.stockMovement.create({
      data: {
        inventoryEntryId: inventoryEntry.id,
        type: "IN",
        quantity,
        reason: "Nhập kho ban đầu",
        reference: `INIT-${product.sku}`,
        notes: `Nhập kho ban đầu ${quantity} sản phẩm ${product.name}`,
      },
    });

    // Random tạo thêm một số stock movements
    const movementCount = Math.floor(Math.random() * 3) + 1; // 1-3 movements
    for (let i = 0; i < movementCount; i++) {
      const movementTypes = ["IN", "OUT", "ADJUSTMENT"];
      const type = movementTypes[
        Math.floor(Math.random() * movementTypes.length)
      ] as "IN" | "OUT" | "ADJUSTMENT";
      const movementQuantity = Math.floor(Math.random() * 20) + 1;

      let reason = "";
      let reference = "";

      switch (type) {
        case "IN":
          reason = "Nhập hàng bổ sung";
          reference = `RESTOCK-${Date.now()}-${i}`;
          break;
        case "OUT":
          reason = "Xuất hàng bán lẻ";
          reference = `SALE-${Date.now()}-${i}`;
          break;
        case "ADJUSTMENT":
          reason = "Điều chỉnh tồn kho";
          reference = `ADJ-${Date.now()}-${i}`;
          break;
      }

      await prisma.stockMovement.create({
        data: {
          inventoryEntryId: inventoryEntry.id,
          type,
          quantity: movementQuantity,
          reason,
          reference,
          notes: `${reason} - ${movementQuantity} sản phẩm`,
        },
      });
    }
  }

  console.log(
    `✅ Inventory seeded successfully for ${products.length} products`
  );
}

async function seedAttributes() {
  console.log("🌱 Seeding attributes...");

  const attributes = [
    {
      name: "Màu sắc",
      slug: "mau-sac",
      description: "Màu sắc của sản phẩm",
      type: "COLOR",
      isRequired: false,
      isFilterable: true,
      sortOrder: 1,
      values: [
        { value: "Đỏ", slug: "do", sortOrder: 1 },
        { value: "Xanh dương", slug: "xanh-duong", sortOrder: 2 },
        { value: "Xanh lá", slug: "xanh-la", sortOrder: 3 },
        { value: "Vàng", slug: "vang", sortOrder: 4 },
        { value: "Đen", slug: "den", sortOrder: 5 },
        { value: "Trắng", slug: "trang", sortOrder: 6 },
        { value: "Hồng", slug: "hong", sortOrder: 7 },
        { value: "Tím", slug: "tim", sortOrder: 8 },
        { value: "Xám", slug: "xam", sortOrder: 9 },
        { value: "Nâu", slug: "nau", sortOrder: 10 },
        { value: "Cam", slug: "cam", sortOrder: 11 },
        { value: "Be", slug: "be", sortOrder: 12 },
        { value: "Navy", slug: "navy", sortOrder: 13 },
        { value: "Khaki", slug: "khaki", sortOrder: 14 },
      ],
    },
    {
      name: "Kích thước",
      slug: "kich-thuoc",
      description: "Kích thước sản phẩm",
      type: "SIZE",
      isRequired: true,
      isFilterable: true,
      sortOrder: 2,
      values: [
        { value: "XS", slug: "xs", sortOrder: 1 },
        { value: "S", slug: "s", sortOrder: 2 },
        { value: "M", slug: "m", sortOrder: 3 },
        { value: "L", slug: "l", sortOrder: 4 },
        { value: "XL", slug: "xl", sortOrder: 5 },
        { value: "XXL", slug: "xxl", sortOrder: 6 },
        { value: "XXXL", slug: "xxxl", sortOrder: 7 },
      ],
    },
    {
      name: "Chất liệu",
      slug: "chat-lieu",
      description: "Chất liệu sản phẩm",
      type: "SELECT",
      isRequired: false,
      isFilterable: true,
      sortOrder: 3,
      values: [
        { value: "Cotton", slug: "cotton", sortOrder: 1 },
        { value: "Polyester", slug: "polyester", sortOrder: 2 },
        { value: "Linen", slug: "linen", sortOrder: 3 },
        { value: "Silk", slug: "silk", sortOrder: 4 },
        { value: "Wool", slug: "wool", sortOrder: 5 },
        { value: "Denim", slug: "denim", sortOrder: 6 },
        { value: "Leather", slug: "leather", sortOrder: 7 },
        { value: "Viscose", slug: "viscose", sortOrder: 8 },
        { value: "Spandex", slug: "spandex", sortOrder: 9 },
        { value: "Nylon", slug: "nylon", sortOrder: 10 },
        { value: "Cashmere", slug: "cashmere", sortOrder: 11 },
        { value: "Modal", slug: "modal", sortOrder: 12 },
      ],
    },
  ];

  const createdAttributes = [];
  for (const attributeData of attributes) {
    const { values, ...attrData } = attributeData;

    const attribute = await prisma.attribute.upsert({
      where: { slug: attrData.slug },
      update: {},
      create: {
        ...attrData,
        type: attrData.type as any, // Cast to AttributeType enum
      },
    });

    createdAttributes.push(attribute);

    // Create attribute values if provided
    if (values && values.length > 0) {
      for (const valueData of values) {
        await prisma.attributeValue.upsert({
          where: {
            attributeId_slug: {
              attributeId: attribute.id,
              slug: valueData.slug,
            },
          },
          update: {},
          create: {
            attributeId: attribute.id,
            ...valueData,
          },
        });
      }
    }
  }

  console.log(
    `✅ Attributes seeded successfully: ${createdAttributes.length} attributes`
  );
  return createdAttributes;
}

async function seedOrders(users: any[]) {
  console.log("🌱 Seeding 20 orders (bills)...");

  const products = await prisma.product.findMany({
    take: 20,
  });

  const statuses = [
    "PENDING",
    "CONFIRMED",
    "PROCESSING",
    "SHIPPED",
    "DELIVERED",
  ];
  const paymentMethods = ["COD", "BANK_TRANSFER"];
  const paymentStatuses = ["PENDING", "PAID"];

  for (let i = 1; i <= 20; i++) {
    const user = users[i % users.length];
    const orderProducts = products.slice(0, Math.floor(Math.random() * 3) + 1); // 1-3 sản phẩm

    let totalAmount = 0;
    const orderItems = orderProducts.map((product) => {
      const quantity = Math.floor(Math.random() * 3) + 1;
      const price = product.salePrice || product.price;
      const total = price * quantity;
      totalAmount += total;

      return {
        productId: product.id,
        quantity,
        price,
        total,
      };
    });

    const shippingFee = totalAmount >= 500000 ? 0 : 30000;
    const finalTotal = totalAmount + shippingFee;

    await prisma.order.create({
      data: {
        userId: user.id,
        status: statuses[i % statuses.length] as any,
        total: finalTotal,
        paymentMethod: paymentMethods[i % paymentMethods.length] as any,
        paymentStatus: paymentStatuses[i % paymentStatuses.length] as any,
        shippingAddress: {
          fullName: user.name,
          phone: user.phone,
          address: `Địa chỉ ${i}, Phường ${i}, Quận ${(i % 12) + 1}`,
          city: "TP. Hồ Chí Minh",
          country: "Việt Nam",
        },
        items: {
          create: orderItems,
        },
      },
    });
  }
}

async function seedPosts(admins: any[], mediaItems: any[]) {
  console.log("🌱 Seeding 20 posts...");

  const categories = await prisma.category.findMany({
    where: { parentId: null }, // Lấy parent categories
  });

  const postImages = mediaItems.filter((m) => m.folder === "posts");

  const postTitles = [
    "Xu hướng thời trang mùa hè 2024",
    "Cách phối đồ công sở thanh lịch",
    "10 mẫu áo sơ mi hot nhất hiện tại",
    "Giày sneaker - Xu hướng không bao giờ lỗi thời",
    "Bí quyết chọn quần jeans phù hợp với từng dáng người",
    "Thời trang vintage đang trở lại mạnh mẽ",
    "Cách chăm sóc quần áo để bền đẹp",
    "Xu hướng màu sắc thời trang năm nay",
    "Phụ kiện không thể thiếu trong tủ đồ",
    "Cách mix đồ đi chơi cuối tuần",
    "Thời trang bền vững - Xu hướng tương lai",
    "10 items cơ bản mọi cô gái nên có",
    "Cách chọn size quần áo chuẩn nhất",
    "Xu hướng áo khoác mùa đông",
    "Phong cách minimalist trong thời trang",
    "Cách phối màu trong trang phục",
    "Thời trang cho dân văn phòng",
    "Giày cao gót - Làm sao để đi thoải mái",
    "Xu hướng túi xách hot trend",
    "Bí quyết mua sắm thông minh",
  ];

  const contentTemplate = `
<h2>Giới thiệu</h2>
<p>Trong thế giới thời trang không ngừng thay đổi, việc nắm bắt được những xu hướng mới nhất là điều vô cùng quan trọng. Bài viết này sẽ chia sẻ với bạn những thông tin hữu ích và cập nhật nhất.</p>

<h3>Nội dung chính</h3>
<p>Chúng ta sẽ cùng khám phá những điều thú vị và bổ ích trong lĩnh vực thời trang. Từ những xu hướng mới nhất đến các bí quyết phối đồ, tất cả đều sẽ được trình bày một cách chi tiết và dễ hiểu.</p>

<h3>Lời khuyên hữu ích</h3>
<ul>
<li>Luôn chọn những món đồ phù hợp với vóc dáng của bạn</li>
<li>Đầu tư vào những items cơ bản, chất lượng tốt</li>
<li>Học cách phối màu harmonious</li>
<li>Đừng quên chú ý đến phụ kiện</li>
</ul>

<h3>Kết luận</h3>
<p>Thời trang không chỉ là cách chúng ta ăn mặc mà còn là cách thể hiện cá tính và phong cách riêng. Hãy luôn tự tin và sáng tạo trong cách phối đồ của mình!</p>
  `;

  for (let i = 1; i <= 20; i++) {
    const title = postTitles[i - 1];
    const slug = title
      .toLowerCase()
      .replace(/\s+/g, "-")
      .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
      .replace(/[èéẹẻẽêềếệểễ]/g, "e")
      .replace(/[ìíịỉĩ]/g, "i")
      .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
      .replace(/[ùúụủũưừứựửữ]/g, "u")
      .replace(/[ỳýỵỷỹ]/g, "y")
      .replace(/[đ]/g, "d")
      .replace(/[^a-z0-9-]/g, "");

    const author = admins[i % admins.length];
    const category = categories[i % categories.length];

    const featuredImageId = postImages[(i - 1) % postImages.length]?.id;

    await prisma.post.upsert({
      where: { slug },
      update: {},
      create: {
        title,
        slug,
        content: contentTemplate,
        excerpt: `${title} - Khám phá những thông tin thú vị và hữu ích trong thế giới thời trang hiện đại.`,
        status: i <= 15 ? "PUBLISHED" : "DRAFT", // 15 bài published, 5 bài draft
        featured: i <= 5, // 5 bài featured
        featuredImageId,
        tags: ["thời trang", "xu hướng", "phong cách"],
        categoryId: category.id,
        authorId: author.id,
        viewCount: Math.floor(Math.random() * 1000),
      },
    });
  }
}

async function seedPages(admins: any[], mediaItems: any[]) {
  console.log("🌱 Seeding pages...");

  const pageImages = mediaItems.filter((m) => m.folder === "pages");

  const pages = [
    {
      title: "Về chúng tôi",
      slug: "ve-chung-toi",
      content: `
<h2>Câu chuyện của NS Shop</h2>
<p>NS Shop được thành lập với sứ mệnh mang đến những sản phẩm thời trang chất lượng cao, phù hợp với xu hướng hiện đại và phong cách riêng của từng khách hàng.</p>

<h3>Tầm nhìn</h3>
<p>Trở thành thương hiệu thời trang hàng đầu Việt Nam, được khách hàng tin tưởng và yêu thích bởi chất lượng sản phẩm và dịch vụ tận tâm.</p>

<h3>Sứ mệnh</h3>
<p>Mang đến cho khách hàng những trải nghiệm mua sắm tuyệt vời với sản phẩm chất lượng, giá cả hợp lý và dịch vụ chăm sóc khách hàng tận tình.</p>

<h3>Giá trị cốt lõi</h3>
<ul>
<li><strong>Chất lượng:</strong> Cam kết cung cấp sản phẩm chất lượng cao</li>
<li><strong>Uy tín:</strong> Xây dựng niềm tin với khách hàng qua từng giao dịch</li>
<li><strong>Sáng tạo:</strong> Không ngừng đổi mới và cập nhật xu hướng</li>
<li><strong>Tận tâm:</strong> Phục vụ khách hàng với tất cả sự chân thành</li>
</ul>
`,
      excerpt:
        "Tìm hiểu về câu chuyện, tầm nhìn và sứ mệnh của NS Shop - thương hiệu thời trang uy tín hàng đầu.",
      metaTitle: "Về chúng tôi - NS Shop | Thương hiệu thời trang uy tín",
      metaDescription:
        "Khám phá câu chuyện thành lập, tầm nhìn và sứ mệnh của NS Shop. Chúng tôi cam kết mang đến sản phẩm thời trang chất lượng cao với dịch vụ tận tâm.",
      featured: true,
    },
    {
      title: "Chính sách bảo hành",
      slug: "chinh-sach-bao-hanh",
      content: `
<h2>Chính sách bảo hành sản phẩm</h2>
<p>NS Shop cam kết bảo hành chất lượng cho tất cả sản phẩm được bán ra với các điều kiện sau:</p>

<h3>Thời gian bảo hành</h3>
<ul>
<li>Quần áo: 30 ngày kể từ ngày mua</li>
<li>Giày dép: 60 ngày kể từ ngày mua</li>
<li>Phụ kiện: 90 ngày kể từ ngày mua</li>
</ul>

<h3>Điều kiện bảo hành</h3>
<ul>
<li>Sản phẩm còn nguyên tem mác, nhãn hiệu</li>
<li>Không có dấu hiệu sử dụng, giặt ủi</li>
<li>Còn hóa đơn mua hàng hoặc biên lai</li>
<li>Lỗi do nhà sản xuất</li>
</ul>

<h3>Quy trình bảo hành</h3>
<ol>
<li>Liên hệ hotline: 1900-xxxx</li>
<li>Mang sản phẩm và hóa đơn đến cửa hàng</li>
<li>Kiểm tra và xác nhận lỗi</li>
<li>Đổi sản phẩm mới hoặc hoàn tiền</li>
</ol>
`,
      excerpt:
        "Tìm hiểu chi tiết về chính sách bảo hành sản phẩm tại NS Shop với thời gian và điều kiện bảo hành rõ ràng.",
      metaTitle: "Chính sách bảo hành - NS Shop",
      metaDescription:
        "Chính sách bảo hành sản phẩm chi tiết tại NS Shop. Thời gian bảo hành lên đến 90 ngày với quy trình đơn giản, nhanh chóng.",
    },
    {
      title: "Hướng dẫn mua hàng",
      slug: "huong-dan-mua-hang",
      content: `
<h2>Hướng dẫn mua hàng online</h2>
<p>Mua sắm tại NS Shop rất đơn giản với 4 bước sau:</p>

<h3>Bước 1: Chọn sản phẩm</h3>
<p>Duyệt qua danh mục sản phẩm, sử dụng bộ lọc để tìm sản phẩm phù hợp. Xem chi tiết sản phẩm, hình ảnh và đánh giá từ khách hàng khác.</p>

<h3>Bước 2: Thêm vào giỏ hàng</h3>
<p>Chọn size, màu sắc và số lượng mong muốn. Nhấn "Thêm vào giỏ hàng" để lưu sản phẩm.</p>

<h3>Bước 3: Thanh toán</h3>
<p>Kiểm tra giỏ hàng, nhập thông tin giao hàng và chọn phương thức thanh toán phù hợp.</p>

<h3>Bước 4: Nhận hàng</h3>
<p>Theo dõi đơn hàng qua email hoặc SMS. Nhận hàng và kiểm tra sản phẩm khi giao hàng.</p>

<h3>Phương thức thanh toán</h3>
<ul>
<li>Thanh toán khi nhận hàng (COD)</li>
<li>Chuyển khoản ngân hàng</li>
<li>Ví điện tử (MoMo, ZaloPay)</li>
<li>Thẻ tín dụng/ghi nợ</li>
</ul>
`,
      excerpt:
        "Hướng dẫn chi tiết cách mua hàng online tại NS Shop với 4 bước đơn giản và nhiều phương thức thanh toán tiện lợi.",
      metaTitle: "Hướng dẫn mua hàng - NS Shop",
      metaDescription:
        "Hướng dẫn mua hàng online tại NS Shop với 4 bước đơn giản. Nhiều phương thức thanh toán tiện lợi, giao hàng nhanh chóng.",
    },
    {
      title: "Chính sách đổi trả",
      slug: "chinh-sach-doi-tra",
      content: `
<h2>Chính sách đổi trả hàng</h2>
<p>NS Shop hỗ trợ đổi trả hàng trong vòng 7 ngày kể từ ngày nhận hàng với các điều kiện sau:</p>

<h3>Điều kiện đổi trả</h3>
<ul>
<li>Sản phẩm còn mới 100%, chưa qua sử dụng</li>
<li>Còn đầy đủ tem mác, nhãn hiệu gốc</li>
<li>Còn hóa đơn mua hàng</li>
<li>Sản phẩm không thuộc danh sách không đổi trả</li>
</ul>

<h3>Sản phẩm không đổi trả</h3>
<ul>
<li>Đồ lót, đồ bơi</li>
<li>Sản phẩm sale trên 50%</li>
<li>Sản phẩm đặt may theo yêu cầu</li>
<li>Phụ kiện cá nhân (khuyên tai, vòng cổ)</li>
</ul>

<h3>Quy trình đổi trả</h3>
<ol>
<li>Liên hệ hotline trong vòng 7 ngày</li>
<li>Gửi sản phẩm về kho (miễn phí ship)</li>
<li>Kiểm tra sản phẩm</li>
<li>Hoàn tiền hoặc gửi sản phẩm mới</li>
</ol>

<h3>Thời gian xử lý</h3>
<p>Đổi hàng: 3-5 ngày làm việc<br>
Trả hàng: 7-10 ngày làm việc</p>
`,
      excerpt:
        "Chính sách đổi trả hàng linh hoạt tại NS Shop trong vòng 7 ngày với quy trình đơn giản và miễn phí ship.",
      metaTitle: "Chính sách đổi trả - NS Shop",
      metaDescription:
        "Chính sách đổi trả hàng tại NS Shop trong vòng 7 ngày. Quy trình đơn giản, miễn phí ship, hoàn tiền nhanh chóng.",
    },
    {
      title: "Liên hệ",
      slug: "lien-he",
      content: `
<h2>Thông tin liên hệ</h2>
<p>Chúng tôi luôn sẵn sàng hỗ trợ bạn! Hãy liên hệ với NS Shop qua các kênh sau:</p>

<h3>Thông tin cửa hàng</h3>
<div class="contact-info">
<p><strong>Địa chỉ:</strong> 123 Đường Nguyễn Huệ, Quận 1, TP.HCM</p>
<p><strong>Điện thoại:</strong> (028) 1234 5678</p>
<p><strong>Hotline:</strong> 1900 xxxx (miễn phí)</p>
<p><strong>Email:</strong> <EMAIL></p>
<p><strong>Website:</strong> www.nsshop.com</p>
</div>

<h3>Giờ làm việc</h3>
<ul>
<li>Thứ 2 - Thứ 6: 8:00 - 22:00</li>
<li>Thứ 7 - Chủ nhật: 9:00 - 21:00</li>
<li>Lễ, Tết: 10:00 - 18:00</li>
</ul>

<h3>Mạng xã hội</h3>
<ul>
<li>Facebook: fb.com/nsshop</li>
<li>Instagram: @nsshop_official</li>
<li>TikTok: @nsshop</li>
<li>YouTube: NS Shop Official</li>
</ul>

<h3>Bản đồ</h3>
<p>Cửa hàng NS Shop nằm tại vị trí thuận tiện, gần các trung tâm thương mại lớn và dễ dàng di chuyển bằng các phương tiện công cộng.</p>
`,
      excerpt:
        "Thông tin liên hệ đầy đủ của NS Shop bao gồm địa chỉ, số điện thoại, email và các kênh mạng xã hội.",
      metaTitle: "Liên hệ - NS Shop",
      metaDescription:
        "Thông tin liên hệ NS Shop: địa chỉ 123 Nguyễn Huệ Q1, hotline 1900 xxxx, email <EMAIL>. Hỗ trợ 24/7.",
    },
  ];

  for (let i = 0; i < pages.length; i++) {
    const pageData = pages[i];
    const author = admins[Math.floor(Math.random() * admins.length)];
    const featuredImageId = pageImages[i % pageImages.length]?.id;

    await prisma.page.upsert({
      where: { slug: pageData.slug },
      update: {},
      create: {
        title: pageData.title,
        slug: pageData.slug,
        content: pageData.content,
        excerpt: pageData.excerpt,
        status: "PUBLISHED",
        featured: pageData.featured || false,
        featuredImageId,
        metaTitle: pageData.metaTitle,
        metaDescription: pageData.metaDescription,
        authorId: author.id,
        viewCount: Math.floor(Math.random() * 500),
      },
    });
  }
}

async function seedMenus(_admins: any[]) {
  console.log("🌱 Seeding menus...");

  // Header Menu
  let headerMenu = await prisma.menu.findFirst({
    where: { name: "Header Menu", location: "header" },
  });

  if (!headerMenu) {
    headerMenu = await prisma.menu.create({
      data: {
        name: "Header Menu",
        location: "header",
        description: "Menu chính hiển thị trên header",
        isActive: true,
      },
    });
  }

  // Footer Menu
  let footerMenu = await prisma.menu.findFirst({
    where: { name: "Footer Menu", location: "footer" },
  });

  if (!footerMenu) {
    footerMenu = await prisma.menu.create({
      data: {
        name: "Footer Menu",
        location: "footer",
        description: "Menu hiển thị ở footer",
        isActive: true,
      },
    });
  }

  // Mobile Menu
  let mobileMenu = await prisma.menu.findFirst({
    where: { name: "Mobile Menu", location: "mobile" },
  });

  if (!mobileMenu) {
    mobileMenu = await prisma.menu.create({
      data: {
        name: "Mobile Menu",
        location: "mobile",
        description: "Menu dành cho thiết bị di động",
        isActive: true,
      },
    });
  }

  // Header Menu Items
  const headerMenuItems = [
    { title: "Trang chủ", url: "/", type: "LINK", order: 1 },
    { title: "Sản phẩm", url: "/products", type: "LINK", order: 2 },
    { title: "Danh mục", url: "/categories", type: "LINK", order: 3 },
    { title: "Về chúng tôi", url: "/about", type: "PAGE", order: 4 },
    { title: "Liên hệ", url: "/contact", type: "PAGE", order: 5 },
  ];

  for (const item of headerMenuItems) {
    const existingItem = await prisma.menuItem.findFirst({
      where: {
        menuId: headerMenu.id,
        title: item.title,
      },
    });

    if (!existingItem) {
      await prisma.menuItem.create({
        data: {
          menuId: headerMenu.id,
          title: item.title,
          url: item.url,
          type: item.type as any,
          order: item.order,
          isActive: true,
        },
      });
    }
  }

  // Footer Menu Items
  const footerMenuItems = [
    { title: "Chính sách bảo mật", url: "/privacy", type: "PAGE", order: 1 },
    { title: "Điều khoản sử dụng", url: "/terms", type: "PAGE", order: 2 },
    { title: "Hướng dẫn mua hàng", url: "/guide", type: "PAGE", order: 3 },
    {
      title: "Chính sách đổi trả",
      url: "/return-policy",
      type: "PAGE",
      order: 4,
    },
    { title: "Hỗ trợ khách hàng", url: "/support", type: "PAGE", order: 5 },
  ];

  for (const item of footerMenuItems) {
    const existingItem = await prisma.menuItem.findFirst({
      where: {
        menuId: footerMenu.id,
        title: item.title,
      },
    });

    if (!existingItem) {
      await prisma.menuItem.create({
        data: {
          menuId: footerMenu.id,
          title: item.title,
          url: item.url,
          type: item.type as any,
          order: item.order,
          isActive: true,
        },
      });
    }
  }

  // Add nested menu items for "Sản phẩm"
  const productMenuItem = await prisma.menuItem.findFirst({
    where: {
      menuId: headerMenu.id,
      title: "Sản phẩm",
    },
  });

  if (productMenuItem) {
    const subMenuItems = [
      { title: "Áo", url: "/categories/ao", type: "CATEGORY", order: 1 },
      { title: "Quần", url: "/categories/quan", type: "CATEGORY", order: 2 },
      { title: "Váy", url: "/categories/vay", type: "CATEGORY", order: 3 },
      {
        title: "Phụ kiện",
        url: "/categories/phu-kien",
        type: "CATEGORY",
        order: 4,
      },
    ];

    for (const item of subMenuItems) {
      const existingSubItem = await prisma.menuItem.findFirst({
        where: {
          menuId: headerMenu.id,
          parentId: productMenuItem.id,
          title: item.title,
        },
      });

      if (!existingSubItem) {
        await prisma.menuItem.create({
          data: {
            menuId: headerMenu.id,
            parentId: productMenuItem.id,
            title: item.title,
            url: item.url,
            type: item.type as any,
            order: item.order,
            isActive: true,
          },
        });
      }
    }
  }

  console.log("✅ Menus seeded successfully");
}

async function seedEvents(admins: any[], mediaItems: any[]) {
  console.log("🌱 Seeding events...");

  const eventImages = mediaItems.filter((m) => m.folder === "events");

  const events = [
    {
      title: "Flash Sale Cuối Tuần",
      description: "Giảm giá lên đến 50% cho tất cả sản phẩm thời trang",
      content:
        "Chương trình khuyến mãi đặc biệt dành cho khách hàng yêu thích thời trang. Giảm giá lên đến 50% cho hơn 1000 sản phẩm hot nhất.",
      slug: "flash-sale-cuoi-tuan",
      type: "SALE",
      status: "PUBLISHED",
      startDate: new Date("2024-12-28T00:00:00Z"),
      endDate: new Date("2024-12-29T23:59:59Z"),
      isAllDay: true,
      location: "Online",
      image: "https://picsum.photos/800/400?random=1001",
      tags: ["sale", "giảm giá", "cuối tuần"],
      seoTitle: "Flash Sale Cuối Tuần - Giảm giá lên đến 50%",
      seoDescription:
        "Đừng bỏ lỡ cơ hội mua sắm với giá ưu đãi trong chương trình Flash Sale cuối tuần",
    },
    {
      title: "Ra Mắt Bộ Sưu Tập Xuân 2025",
      description: "Khám phá những xu hướng thời trang mới nhất cho mùa xuân",
      content:
        "Bộ sưu tập xuân 2025 với những thiết kế tươi mới, màu sắc rực rỡ và phong cách hiện đại.",
      slug: "ra-mat-bo-suu-tap-xuan-2025",
      type: "LAUNCH",
      status: "PUBLISHED",
      startDate: new Date("2025-01-15T10:00:00Z"),
      endDate: new Date("2025-01-15T18:00:00Z"),
      location: "NS Shop Flagship Store",
      maxAttendees: 100,
      currentAttendees: 0,
      price: 0,
      image: "https://picsum.photos/800/400?random=1002",
      tags: ["ra mắt", "bộ sưu tập", "xuân 2025"],
      seoTitle: "Ra Mắt Bộ Sưu Tập Xuân 2025 - NS Shop",
      seoDescription:
        "Tham gia sự kiện ra mắt bộ sưu tập xuân 2025 với những thiết kế độc đáo",
    },
    {
      title: "Workshop: Phối Đồ Chuyên Nghiệp",
      description: "Học cách phối đồ chuyên nghiệp từ các stylist hàng đầu",
      content:
        "Workshop thực hành về cách phối đồ cho môi trường công sở và các dịp đặc biệt.",
      slug: "workshop-phoi-do-chuyen-nghiep",
      type: "WORKSHOP",
      status: "PUBLISHED",
      startDate: new Date("2025-02-01T14:00:00Z"),
      endDate: new Date("2025-02-01T17:00:00Z"),
      location: "NS Shop Training Center",
      maxAttendees: 30,
      currentAttendees: 0,
      price: 200000,
      image: "https://picsum.photos/800/400?random=1003",
      tags: ["workshop", "phối đồ", "stylist"],
      seoTitle: "Workshop Phối Đồ Chuyên Nghiệp - NS Shop",
      seoDescription:
        "Tham gia workshop học cách phối đồ chuyên nghiệp từ các chuyên gia",
    },
    {
      title: "Webinar: Xu Hướng Thời Trang 2025",
      description: "Khám phá những xu hướng thời trang sẽ thống trị năm 2025",
      content:
        "Webinar trực tuyến với sự tham gia của các chuyên gia thời trang quốc tế.",
      slug: "webinar-xu-huong-thoi-trang-2025",
      type: "WEBINAR",
      status: "PUBLISHED",
      startDate: new Date("2025-01-20T19:00:00Z"),
      endDate: new Date("2025-01-20T21:00:00Z"),
      isAllDay: false,
      location: "Online - Zoom",
      maxAttendees: 500,
      currentAttendees: 0,
      price: 0,
      image: "https://picsum.photos/800/400?random=1004",
      tags: ["webinar", "xu hướng", "thời trang 2025"],
      seoTitle: "Webinar Xu Hướng Thời Trang 2025",
      seoDescription: "Tham gia webinar miễn phí về xu hướng thời trang 2025",
    },
    {
      title: "Khuyến Mãi Tết Nguyên Đán",
      description: "Ưu đãi đặc biệt mừng Tết Nguyên Đán 2025",
      content:
        "Chương trình khuyến mãi lớn nhất trong năm với nhiều ưu đãi hấp dẫn.",
      slug: "khuyen-mai-tet-nguyen-dan",
      type: "SEASONAL",
      status: "DRAFT",
      startDate: new Date("2025-01-25T00:00:00Z"),
      endDate: new Date("2025-02-05T23:59:59Z"),
      isAllDay: true,
      location: "Toàn bộ hệ thống",
      image: "https://picsum.photos/800/400?random=1005",
      tags: ["tết", "khuyến mãi", "seasonal"],
      seoTitle: "Khuyến Mãi Tết Nguyên Đán 2025 - NS Shop",
      seoDescription:
        "Ưu đãi đặc biệt mừng Tết Nguyên Đán với nhiều quà tặng hấp dẫn",
    },
  ];

  for (let i = 0; i < events.length; i++) {
    const eventData = events[i];
    const author = admins[Math.floor(Math.random() * admins.length)];
    const imageId = eventImages[i % eventImages.length]?.id;

    await prisma.event.upsert({
      where: { slug: eventData.slug },
      update: {},
      create: {
        title: eventData.title,
        description: eventData.description,
        content: eventData.content,
        slug: eventData.slug,
        type: eventData.type as any,
        status: eventData.status as any,
        startDate: eventData.startDate,
        endDate: eventData.endDate,
        isAllDay: eventData.isAllDay,
        location: eventData.location,
        maxAttendees: eventData.maxAttendees,
        currentAttendees: eventData.currentAttendees,
        price: eventData.price,
        imageId,
        tags: eventData.tags,
        seoTitle: eventData.seoTitle,
        seoDescription: eventData.seoDescription,
        createdBy: author.id,
      },
    });
  }

  console.log("✅ Events seeded successfully");
}

async function seedNotifications(admins: any[]) {
  console.log("🌱 Seeding notifications...");

  const notificationTemplates = [
    {
      title: "Đơn hàng mới cần xử lý",
      message:
        "Có đơn hàng mới #{orderId} từ khách hàng {customerName} cần được xử lý.",
      type: "INFO",
      priority: "NORMAL",
      targetType: "ALL_ADMINS",
      actionUrl: "/admin/orders/{orderId}",
    },
    {
      title: "Sản phẩm sắp hết hàng",
      message: "Sản phẩm {productName} chỉ còn {quantity} sản phẩm trong kho.",
      type: "WARNING",
      priority: "HIGH",
      targetType: "ALL_ADMINS",
      actionUrl: "/admin/inventory",
    },
    {
      title: "Đánh giá sản phẩm mới",
      message:
        "Khách hàng {customerName} vừa đánh giá {rating} sao cho sản phẩm {productName}.",
      type: "INFO",
      priority: "LOW",
      targetType: "ALL_ADMINS",
      actionUrl: "/admin/reviews",
    },
    {
      title: "Thanh toán thành công",
      message:
        "Đơn hàng #{orderId} đã được thanh toán thành công với số tiền {amount}.",
      type: "SUCCESS",
      priority: "NORMAL",
      targetType: "ALL_ADMINS",
      actionUrl: "/admin/orders",
    },
    {
      title: "Lỗi hệ thống",
      message: "Phát hiện lỗi trong quá trình xử lý: {errorMessage}",
      type: "ERROR",
      priority: "URGENT",
      targetType: "ALL_ADMINS",
      actionUrl: "/admin/audit-logs",
    },
    {
      title: "Khách hàng mới đăng ký",
      message: "Khách hàng {customerName} ({email}) vừa đăng ký tài khoản mới.",
      type: "INFO",
      priority: "LOW",
      targetType: "ALL_ADMINS",
      actionUrl: "/admin/customers",
    },
    {
      title: "Cập nhật hệ thống",
      message: "Hệ thống đã được cập nhật lên phiên bản {version} thành công.",
      type: "SYSTEM",
      priority: "NORMAL",
      targetType: "ALL_ADMINS",
    },
    {
      title: "Báo cáo doanh thu hàng ngày",
      message: "Doanh thu hôm nay: {revenue}. Tổng số đơn hàng: {orderCount}.",
      type: "INFO",
      priority: "NORMAL",
      targetType: "ALL_ADMINS",
      actionUrl: "/admin/analytics",
    },
    {
      title: "Yêu cầu hỗ trợ mới",
      message:
        "Khách hàng {customerName} vừa gửi yêu cầu hỗ trợ: {supportMessage}",
      type: "INFO",
      priority: "HIGH",
      targetType: "ALL_ADMINS",
      actionUrl: "/admin/support",
    },
    {
      title: "Sản phẩm mới được thêm",
      message:
        "Sản phẩm {productName} đã được thêm vào danh mục {categoryName}.",
      type: "INFO",
      priority: "LOW",
      targetType: "ROLE_ADMIN",
      actionUrl: "/admin/products",
    },
    {
      title: "Đơn hàng bị hủy",
      message: "Đơn hàng #{orderId} đã bị hủy bởi khách hàng {customerName}.",
      type: "WARNING",
      priority: "NORMAL",
      targetType: "ALL_ADMINS",
      actionUrl: "/admin/orders",
    },
    {
      title: "Backup dữ liệu hoàn tất",
      message:
        "Backup dữ liệu hệ thống đã được thực hiện thành công lúc {backupTime}.",
      type: "SYSTEM",
      priority: "LOW",
      targetType: "ROLE_ADMIN",
    },
  ];

  // Tạo 25 notifications với data ngẫu nhiên và phân phối hợp lý
  for (let i = 1; i <= 25; i++) {
    const template = notificationTemplates[i % notificationTemplates.length];
    const createdBy = admins[Math.floor(Math.random() * admins.length)];

    // Logic phân phối targetType thông minh hơn
    let targetType = template.targetType;
    let targetId = null;

    if (template.targetType === "ALL_ADMINS") {
      // 80% ALL_ADMINS, 20% SPECIFIC_ADMIN
      if (Math.random() > 0.8) {
        targetType = "SPECIFIC_ADMIN";
        targetId = admins[Math.floor(Math.random() * admins.length)].id;
      }
    } else if (template.targetType === "ROLE_ADMIN") {
      // Chỉ gửi cho admin có role ADMIN
      const adminUsers = admins.filter((admin) => admin.role === "ADMIN");
      if (adminUsers.length > 0 && Math.random() > 0.5) {
        targetType = "SPECIFIC_ADMIN";
        targetId = adminUsers[Math.floor(Math.random() * adminUsers.length)].id;
      } else {
        targetType = "ROLE_ADMIN";
      }
    }

    // Random data để thay thế placeholders
    const sampleData = {
      orderId: `ORD${String(Math.floor(Math.random() * 1000)).padStart(3, "0")}`,
      customerName: `Khách hàng ${Math.floor(Math.random() * 100)}`,
      productName: `Sản phẩm ${Math.floor(Math.random() * 50)}`,
      categoryName: `Danh mục ${Math.floor(Math.random() * 10)}`,
      quantity: Math.floor(Math.random() * 10) + 1,
      rating: Math.floor(Math.random() * 5) + 1,
      amount:
        (Math.floor(Math.random() * 1000000) + 100000).toLocaleString("vi-VN") +
        " VND",
      errorMessage: "Database connection timeout",
      email: `user${Math.floor(Math.random() * 100)}@example.com`,
      version: "2.1.0",
      revenue:
        (Math.floor(Math.random() * 10000000) + 1000000).toLocaleString(
          "vi-VN"
        ) + " VND",
      orderCount: Math.floor(Math.random() * 50) + 10,
      supportMessage: "Tôi cần hỗ trợ về việc đổi trả sản phẩm",
      backupTime: new Date().toLocaleString("vi-VN"),
    };

    // Thay thế placeholders trong message
    let message = template.message;
    Object.entries(sampleData).forEach(([key, value]) => {
      message = message.replace(new RegExp(`{${key}}`, "g"), value.toString());
    });

    // Thay thế placeholders trong actionUrl
    let actionUrl = template.actionUrl;
    if (actionUrl) {
      Object.entries(sampleData).forEach(([key, value]) => {
        actionUrl = actionUrl?.replace(
          new RegExp(`{${key}}`, "g"),
          value.toString()
        );
      });
    }

    // Tạo thời gian ngẫu nhiên trong 30 ngày qua, với bias về thời gian gần đây
    const daysAgo = Math.floor(Math.random() * Math.random() * 30); // Bias về số nhỏ hơn
    const hoursAgo = Math.floor(Math.random() * 24);
    const minutesAgo = Math.floor(Math.random() * 60);

    const createdAt = new Date(
      Date.now() -
        daysAgo * 24 * 60 * 60 * 1000 -
        hoursAgo * 60 * 60 * 1000 -
        minutesAgo * 60 * 1000
    );

    // 70% đã đọc, 30% chưa đọc (thực tế hơn)
    const isRead = Math.random() > 0.3;

    await prisma.notification.create({
      data: {
        title: template.title,
        message,
        type: template.type as any,
        priority: template.priority as any,
        targetType: targetType as any,
        targetId,
        isRead,
        readAt: isRead
          ? new Date(
              createdAt.getTime() +
                Math.floor(Math.random() * 24 * 60 * 60 * 1000)
            )
          : null,
        actionUrl,
        createdBy: createdBy.id,
        createdAt,
        expiresAt:
          Math.random() > 0.9
            ? new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
            : null, // 10% có expiry
      },
    });
  }
}

async function seedAuditLogs(admins: any[]) {
  console.log("🌱 Seeding audit logs...");

  const auditActions = [
    {
      action: "CREATE",
      resource: "Product",
      description: "Tạo sản phẩm mới",
      newValues: {
        name: "Áo sơ mi trắng",
        price: 299000,
        category: "Áo sơ mi",
        status: "active",
      },
    },
    {
      action: "UPDATE",
      resource: "Product",
      description: "Cập nhật thông tin sản phẩm",
      oldValues: {
        price: 299000,
        stock: 50,
      },
      newValues: {
        price: 259000,
        stock: 45,
      },
    },
    {
      action: "DELETE",
      resource: "Product",
      description: "Xóa sản phẩm",
      oldValues: {
        name: "Áo khoác cũ",
        status: "inactive",
      },
    },
    {
      action: "CREATE",
      resource: "User",
      description: "Tạo tài khoản khách hàng mới",
      newValues: {
        email: "<EMAIL>",
        name: "Nguyễn Văn A",
        role: "customer",
      },
    },
    {
      action: "UPDATE",
      resource: "Order",
      description: "Cập nhật trạng thái đơn hàng",
      oldValues: {
        status: "pending",
      },
      newValues: {
        status: "processing",
      },
    },
    {
      action: "CREATE",
      resource: "Category",
      description: "Tạo danh mục mới",
      newValues: {
        name: "Áo khoác mùa đông",
        slug: "ao-khoac-mua-dong",
        isActive: true,
      },
    },
    {
      action: "UPDATE",
      resource: "Setting",
      description: "Cập nhật cài đặt hệ thống",
      oldValues: {
        shippingFee: 30000,
      },
      newValues: {
        shippingFee: 35000,
      },
    },
    {
      action: "DELETE",
      resource: "Review",
      description: "Xóa đánh giá không phù hợp",
      oldValues: {
        rating: 1,
        comment: "Sản phẩm không tốt",
        status: "spam",
      },
    },
    {
      action: "CREATE",
      resource: "Brand",
      description: "Thêm thương hiệu mới",
      newValues: {
        name: "Fashion Brand",
        slug: "fashion-brand",
        isActive: true,
      },
    },
    {
      action: "UPDATE",
      resource: "Inventory",
      description: "Cập nhật tồn kho",
      oldValues: {
        quantity: 100,
        available: 95,
      },
      newValues: {
        quantity: 120,
        available: 115,
      },
    },
  ];

  const ipAddresses = [
    "*************",
    "*********",
    "***********",
    "*************",
    "************",
  ];

  const userAgents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
  ];

  // Tạo 50 audit logs với data ngẫu nhiên
  for (let i = 1; i <= 50; i++) {
    const actionTemplate = auditActions[i % auditActions.length];
    const admin = admins[Math.floor(Math.random() * admins.length)];
    const ipAddress =
      ipAddresses[Math.floor(Math.random() * ipAddresses.length)];
    const userAgent = userAgents[Math.floor(Math.random() * userAgents.length)];

    // Random resource ID
    const resourceId = `${actionTemplate.resource.toLowerCase()}_${Math.floor(Math.random() * 1000)}`;

    // Random created time trong 60 ngày qua
    const createdAt = new Date(
      Date.now() - Math.floor(Math.random() * 60) * 24 * 60 * 60 * 1000
    );

    await prisma.auditLog.create({
      data: {
        action: actionTemplate.action,
        resource: actionTemplate.resource,
        resourceId,
        oldValues: actionTemplate.oldValues || undefined,
        newValues: actionTemplate.newValues || undefined,
        description: actionTemplate.description,
        ipAddress,
        userAgent,
        adminId: admin.id,
        createdAt,
      },
    });
  }
}

async function main() {
  console.log("🚀 Starting NS Shop database seeding...");

  try {
    const { admin, moderator } = await seedBasicData();
    const mediaItems = await seedMedia();
    const brands = await seedBrands(mediaItems);
    const users = await seedUsers();
    await seedSettings();
    await seedCategories();
    const attributes = await seedAttributes();
    await seedSampleProducts(mediaItems);
    await seedInventory();
    await seedOrders(users);
    await seedPosts([admin, moderator], mediaItems);
    await seedPages([admin, moderator], mediaItems);
    await seedMenus([admin, moderator]);
    await seedEvents([admin, moderator], mediaItems);
    await seedNotifications([admin, moderator]);
    await seedAuditLogs([admin, moderator]);
    await seedEmailTemplates();

    console.log("✅ Database seeding completed successfully!");
    console.log("\n📧 Login credentials:");
    console.log("Admin: <EMAIL> / admin123");
    console.log("Moderator: <EMAIL> / moderator123");
    console.log("Users: <EMAIL> / user1123 (user1-user10)");
    console.log("\n📊 Seeded data summary:");
    console.log("- 2 admin accounts (1 admin, 1 moderator)");
    console.log("- 10 user accounts");
    console.log(`- ${brands.length} brands`);
    console.log(`- ${attributes.length} attributes with values`);
    console.log("- 50 products with inventory entries");
    console.log("- Categories with subcategories");
    console.log("- Inventory entries with stock movements");
    console.log("- 20 orders (bills)");
    console.log("- 20 posts");
    console.log("- 5 pages (about, policies, contact, etc.)");
    console.log("- 3 menus with nested menu items (header, footer, mobile)");
    console.log("- 5 events (sales, workshops, webinars, launches)");
    console.log("- 25 notifications (read/unread with smart targeting)");
    console.log("- 50 audit logs (admin actions tracking)");
    console.log("- 5 email templates (welcome, order, reset, etc.)");
    console.log("- Default settings");
  } catch (error) {
    console.error("❌ Database seeding failed:", error);
    process.exit(1);
  }
}

main()
  .catch((e) => {
    console.error("❌ Seed error:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
