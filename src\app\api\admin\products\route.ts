import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { jwtVerify } from "jose";

// Helper function to verify admin JWT token
async function verifyAdminToken(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;
    if (!adminToken) return null;

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );
    const { payload } = await jwtVerify(adminToken, secret);
    return payload;
  } catch {
    return null;
  }
}

// GET /api/admin/products - <PERSON><PERSON><PERSON> danh sách sản phẩm cho admin
export async function GET(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);

    if (!adminToken || adminToken.type !== "admin") {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Both ADMIN and MODERATOR can manage products
    if (adminToken.role !== "ADMIN" && adminToken.role !== "MODERATOR") {
      return NextResponse.json(
        { success: false, error: "Không có quyền quản lý sản phẩm" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const category = searchParams.get("category");
    const brand = searchParams.get("brand");
    const search = searchParams.get("search");
    const status = searchParams.get("status");
    const featured = searchParams.get("featured");

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (category) {
      where.categoryId = category;
    }

    if (brand) {
      where.brandId = brand;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { sku: { contains: search, mode: "insensitive" } },
      ];
    }

    if (status) {
      where.status = status;
    }

    if (featured === "true") {
      where.featured = true;
    } else if (featured === "false") {
      where.featured = false;
    }

    // Get products with pagination
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          brand: {
            select: {
              id: true,
              name: true,
              logo: {
                select: {
                  id: true,
                  url: true,
                  alt: true,
                },
              },
            },
          },
          media: {
            include: {
              media: {
                select: {
                  id: true,
                  url: true,
                  alt: true,
                  title: true,
                  width: true,
                  height: true,
                },
              },
            },
            orderBy: [{ isPrimary: "desc" }, { order: "asc" }],
          },
          ProductAttribute: {
            include: {
              attribute: {
                select: {
                  id: true,
                  name: true,
                  type: true,
                },
              },
              attributeValue: {
                select: {
                  id: true,
                  value: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
      }),
      prisma.product.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: products,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Get admin products error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy danh sách sản phẩm" },
      { status: 500 }
    );
  }
}

// POST /api/admin/products - Tạo sản phẩm mới
export async function POST(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);

    if (!adminToken || adminToken.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const {
      name,
      description,
      price,
      salePrice,
      sku,
      stock,
      categoryId,
      brandId,
      images, // Array of ProductImage objects with media info
      featured,
      status,
      tags,
      slug,
      attributes,
    } = body;

    // Enhanced validation
    const validationErrors: string[] = [];

    if (!name || name.trim().length === 0) {
      validationErrors.push("Tên sản phẩm là bắt buộc");
    } else if (name.length < 3) {
      validationErrors.push("Tên sản phẩm phải có ít nhất 3 ký tự");
    } else if (name.length > 255) {
      validationErrors.push("Tên sản phẩm không được vượt quá 255 ký tự");
    }

    if (!description || description.trim().length === 0) {
      validationErrors.push("Mô tả sản phẩm là bắt buộc");
    } else if (description.length < 10) {
      validationErrors.push("Mô tả sản phẩm phải có ít nhất 10 ký tự");
    }

    if (!categoryId) {
      validationErrors.push("Danh mục là bắt buộc");
    }

    if (!price || parseFloat(price) <= 0) {
      validationErrors.push("Giá sản phẩm phải lớn hơn 0");
    } else if (parseFloat(price) > 999999999) {
      validationErrors.push("Giá sản phẩm không được vượt quá 999,999,999 VND");
    }

    if (salePrice && parseFloat(salePrice) >= parseFloat(price)) {
      validationErrors.push("Giá khuyến mãi phải nhỏ hơn giá gốc");
    }

    if (stock && parseInt(stock) < 0) {
      validationErrors.push("Số lượng tồn kho không được âm");
    }

    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: validationErrors },
        { status: 400 }
      );
    }

    // Generate SKU if not provided
    const finalSku = sku || `PRD-${Date.now()}`;

    // Check if SKU already exists
    const existingSku = await prisma.product.findUnique({
      where: { sku: finalSku },
    });

    if (existingSku) {
      return NextResponse.json({ error: "SKU đã tồn tại" }, { status: 400 });
    }

    // Check if slug already exists
    const existingSlug = await prisma.product.findUnique({
      where: { slug },
    });

    if (existingSlug) {
      return NextResponse.json({ error: "Slug đã tồn tại" }, { status: 400 });
    }

    // Create product with attributes in transaction
    const product = await prisma.$transaction(async (tx) => {
      // Create the product
      const newProduct = await tx.product.create({
        data: {
          name,
          description,
          price: parseFloat(price),
          salePrice: salePrice ? parseFloat(salePrice) : null,
          sku: finalSku,
          stock: parseInt(stock) || 0,
          categoryId,
          brandId: brandId || null,
          featured: Boolean(featured),
          status: status || "ACTIVE",
          tags: tags || [],
          slug,
        },
      });

      // Handle attributes
      if (attributes && Array.isArray(attributes) && attributes.length > 0) {
        // Create product attributes
        await tx.productAttribute.createMany({
          data: attributes.map((attr: any) => ({
            productId: newProduct.id,
            attributeId: attr.attributeId,
            attributeValueId: attr.attributeValueId,
          })),
        });
      }

      // Handle media relationships
      if (images && Array.isArray(images) && images.length > 0) {
        const mediaData = images
          .filter((img: any) => img.id) // Only process images with media ID
          .map((img: any, index: number) => ({
            productId: newProduct.id,
            mediaId: img.id,
            order: img.order || index,
            isPrimary: img.isPrimary || index === 0,
          }));

        if (mediaData.length > 0) {
          await tx.productMedia.createMany({
            data: mediaData,
          });
        }
      }

      return newProduct;
    });

    // Fetch the created product with relations
    const createdProduct = await prisma.product.findUnique({
      where: { id: product.id },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        brand: {
          select: {
            id: true,
            name: true,
            logo: {
              select: {
                id: true,
                url: true,
                alt: true,
              },
            },
          },
        },
        media: {
          include: {
            media: {
              select: {
                id: true,
                url: true,
                alt: true,
                title: true,
                width: true,
                height: true,
              },
            },
          },
          orderBy: [{ isPrimary: "desc" }, { order: "asc" }],
        },
        ProductAttribute: {
          include: {
            attribute: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
            attributeValue: {
              select: {
                id: true,
                value: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json(
      {
        success: true,
        data: createdProduct,
        message: "Sản phẩm đã được tạo thành công",
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Create product error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tạo sản phẩm" },
      { status: 500 }
    );
  }
}
