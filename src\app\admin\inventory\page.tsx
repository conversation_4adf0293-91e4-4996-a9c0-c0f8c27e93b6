"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import {
  Package,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Search,
  Filter,
  Plus,
  Edit,
  Eye,
  MoreHorizontal,
  Home,
  DollarSign,
  ShoppingCart,
  AlertCircle,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { AdminProductImage } from "@/components/admin/AdminImage";
import {
  InventoryEntry,
  InventoryStats,
  InventoryFilters,
  PaginationParams,
  StockMovement,
  Product,
} from "@/types";
import { getFirstAvailableImage } from "@/lib/admin/image-utils";

interface InventoryResponse {
  success: boolean;
  data: (InventoryEntry & {
    product: {
      id: string;
      name: string;
      sku: string;
      images: string[];
      price: number;
      category: { name: string };
      brand?: { name: string };
    };
    stockMovements: StockMovement[];
  })[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface StatsResponse {
  success: boolean;
  data: InventoryStats;
}

export default function AdminInventoryPage() {
  const [inventoryEntries, setInventoryEntries] = useState<
    InventoryResponse["data"]
  >([]);
  const [stats, setStats] = useState<InventoryStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [statsLoading, setStatsLoading] = useState(true);
  const [filters, setFilters] = useState<InventoryFilters>({
    search: "",
    location: "",
    lowStock: false,
    outOfStock: false,
  });
  const [pagination, setPagination] = useState<
    PaginationParams & {
      page: number;
      limit: number;
      total?: number;
      totalPages?: number;
    }
  >({
    page: 1,
    limit: 10,
    sortBy: "createdAt",
    sortOrder: "desc",
  });

  // Fetch inventory stats
  const fetchStats = async () => {
    setStatsLoading(true);
    try {
      const response = await fetch("/api/admin/inventory/stats");
      const data: StatsResponse = await response.json();

      if (response.ok && data.success) {
        setStats(data.data);
      } else {
        toast.error("Có lỗi xảy ra khi tải thống kê kho hàng");
      }
    } catch (error) {
      console.error("Fetch stats error:", error);
      toast.error("Có lỗi xảy ra khi tải thống kê kho hàng");
    } finally {
      setStatsLoading(false);
    }
  };

  // Fetch inventory entries
  const fetchInventory = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(filters.search && { search: filters.search }),
        ...(filters.location && { location: filters.location }),
        ...(filters.lowStock && { lowStock: "true" }),
        ...(filters.outOfStock && { outOfStock: "true" }),
        ...(filters.brandId && { brandId: filters.brandId }),
        ...(filters.categoryId && { categoryId: filters.categoryId }),
        sortBy: pagination.sortBy || "createdAt",
        sortOrder: pagination.sortOrder || "desc",
      });

      const response = await fetch(`/api/admin/inventory?${params}`);
      const data: InventoryResponse = await response.json();

      if (response.ok && data.success) {
        setInventoryEntries(data.data || []);
        setPagination((prev) => ({ ...prev, ...data.pagination }));
      } else {
        toast.error("Có lỗi xảy ra khi tải danh sách kho hàng");
      }
    } catch (error) {
      console.error("Fetch inventory error:", error);
      toast.error("Có lỗi xảy ra khi tải danh sách kho hàng");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  useEffect(() => {
    fetchInventory();
  }, [filters, pagination.page, pagination.sortBy, pagination.sortOrder]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination((prev) => ({ ...prev, page: 1 }));
    fetchInventory();
  };

  // Handle filter change
  const handleFilterChange = (key: keyof InventoryFilters, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  // Format currency (unused for now but may be needed later)
  // const formatCurrency = (amount: number) => {
  //   return new Intl.NumberFormat("vi-VN", {
  //     style: "currency",
  //     currency: "VND",
  //   }).format(amount);
  // };

  // Get stock status
  const getStockStatus = (entry: InventoryResponse["data"][0]) => {
    if (entry.available <= 0) {
      return {
        label: "Hết hàng",
        color: "bg-red-100 text-red-800",
        icon: AlertCircle,
      };
    } else if (entry.available <= entry.minStock && entry.minStock > 0) {
      return {
        label: "Sắp hết",
        color: "bg-yellow-100 text-yellow-800",
        icon: AlertTriangle,
      };
    } else {
      return {
        label: "Còn hàng",
        color: "bg-green-100 text-green-800",
        icon: Package,
      };
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Quản lý kho hàng</h1>
          <p className="text-muted-foreground">
            Theo dõi và quản lý tồn kho sản phẩm
          </p>
        </div>
        <div className="flex gap-2">
          <Link href="/admin/inventory/movements">
            <Button variant="outline">
              <TrendingUp className="h-4 w-4 mr-2" />
              Lịch sử giao dịch
            </Button>
          </Link>
          <Link href="/admin/inventory/create">
            <Button className="bg-pink-600 hover:bg-pink-700">
              <Plus className="h-4 w-4 mr-2" />
              Thêm kho hàng
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Tổng sản phẩm
                </p>
                <p className="text-2xl font-bold">
                  {statsLoading ? "..." : stats?.totalProducts || 0}
                </p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Tổng tồn kho
                </p>
                <p className="text-2xl font-bold">
                  {statsLoading ? "..." : stats?.totalStock || 0}
                </p>
              </div>
              <Home className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Sắp hết hàng
                </p>
                <p className="text-2xl font-bold text-yellow-600">
                  {statsLoading ? "..." : stats?.lowStockProducts || 0}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Hết hàng
                </p>
                <p className="text-2xl font-bold text-red-600">
                  {statsLoading ? "..." : stats?.outOfStockProducts || 0}
                </p>
              </div>
              <AlertCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alerts */}
      {stats?.alerts && stats.alerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-600">
              <AlertTriangle className="h-5 w-5" />
              Cảnh báo kho hàng ({stats.alerts.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {stats.alerts.slice(0, 5).map((alert) => (
                <div
                  key={alert.id}
                  className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200"
                >
                  <div className="flex items-center gap-3">
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    <span className="text-sm">{alert.message}</span>
                  </div>
                  <Link href={`/admin/inventory/${alert.id}`}>
                    <Button size="sm" variant="outline">
                      Xem chi tiết
                    </Button>
                  </Link>
                </div>
              ))}
              {stats.alerts.length > 5 && (
                <p className="text-sm text-muted-foreground text-center pt-2">
                  Và {stats.alerts.length - 5} cảnh báo khác...
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <form
            onSubmit={handleSearch}
            className="flex gap-4 items-end flex-wrap"
          >
            <div className="flex-1 min-w-64">
              <Input
                placeholder="Tìm kiếm sản phẩm, SKU..."
                value={filters.search}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, search: e.target.value }))
                }
                className="w-full"
              />
            </div>
            <Select
              value={
                filters.lowStock ? "low" : filters.outOfStock ? "out" : "all"
              }
              onValueChange={(value) => {
                if (value === "low") {
                  handleFilterChange("lowStock", true);
                  handleFilterChange("outOfStock", false);
                } else if (value === "out") {
                  handleFilterChange("outOfStock", true);
                  handleFilterChange("lowStock", false);
                } else {
                  handleFilterChange("lowStock", false);
                  handleFilterChange("outOfStock", false);
                }
              }}
            >
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Trạng thái kho" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                <SelectItem value="low">Sắp hết hàng</SelectItem>
                <SelectItem value="out">Hết hàng</SelectItem>
              </SelectContent>
            </Select>
            <Button type="submit" variant="outline">
              <Search className="h-4 w-4 mr-2" />
              Tìm kiếm
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Inventory List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Home className="h-5 w-5 text-pink-600" />
            Danh sách kho hàng
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600"></div>
            </div>
          ) : inventoryEntries.length === 0 ? (
            <div className="text-center py-12">
              <Package className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                Chưa có thông tin kho hàng
              </h3>
              <p className="text-muted-foreground mb-4">
                Bắt đầu bằng cách thêm thông tin kho hàng cho sản phẩm
              </p>
              <Link href="/admin/inventory/create">
                <Button className="bg-pink-600 hover:bg-pink-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm kho hàng
                </Button>
              </Link>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">Sản phẩm</th>
                    <th className="text-left py-3 px-4">SKU</th>
                    <th className="text-left py-3 px-4">Tồn kho</th>
                    <th className="text-left py-3 px-4">Có sẵn</th>
                    <th className="text-left py-3 px-4">Đã đặt</th>
                    <th className="text-left py-3 px-4">Mức tối thiểu</th>
                    <th className="text-left py-3 px-4">Vị trí</th>
                    <th className="text-left py-3 px-4">Trạng thái</th>
                    <th className="text-left py-3 px-4">Thao tác</th>
                  </tr>
                </thead>
                <tbody>
                  {inventoryEntries.map((entry) => {
                    const status = getStockStatus(entry);
                    const StatusIcon = status.icon;

                    return (
                      <tr key={entry.id} className="border-b hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-3">
                            <AdminProductImage
                              src={getFirstAvailableImage(entry.product)}
                              alt={entry.product.name}
                              size={48}
                            />
                            <div>
                              <p className="font-medium">
                                {entry.product.name}
                              </p>
                              <p className="text-sm text-gray-500">
                                {entry.product.category.name}
                                {entry.product.brand &&
                                  ` • ${entry.product.brand.name}`}
                              </p>
                            </div>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <span className="font-mono text-sm">
                            {entry.product.sku}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <span className="font-semibold">
                            {entry.quantity}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <span className="font-semibold text-green-600">
                            {entry.available}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <span className="font-semibold text-orange-600">
                            {entry.reserved}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <span className="text-sm">{entry.minStock}</span>
                        </td>
                        <td className="py-3 px-4">
                          <span className="text-sm">
                            {entry.location || "—"}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <Badge className={status.color}>
                            <StatusIcon className="h-3 w-3 mr-1" />
                            {status.label}
                          </Badge>
                        </td>
                        <td className="py-3 px-4">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem asChild>
                                <Link href={`/admin/inventory/${entry.id}`}>
                                  <Eye className="h-4 w-4 mr-2" />
                                  Xem chi tiết
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem asChild>
                                <Link
                                  href={`/admin/inventory/${entry.id}/edit`}
                                >
                                  <Edit className="h-4 w-4 mr-2" />
                                  Chỉnh sửa
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem asChild>
                                <Link
                                  href={`/admin/inventory/movements/create?inventoryId=${entry.id}`}
                                >
                                  <TrendingUp className="h-4 w-4 mr-2" />
                                  Giao dịch kho
                                </Link>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {!loading && inventoryEntries.length > 0 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Hiển thị {(pagination.page - 1) * pagination.limit + 1} đến{" "}
            {Math.min(
              pagination.page * pagination.limit,
              pagination.total || 0
            )}{" "}
            trong tổng số {pagination.total || 0} mục
          </p>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setPagination((prev) => ({ ...prev, page: prev.page - 1 }))
              }
              disabled={pagination.page <= 1}
            >
              Trước
            </Button>
            <span className="text-sm">
              Trang {pagination.page} / {pagination.totalPages || 1}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setPagination((prev) => ({ ...prev, page: prev.page + 1 }))
              }
              disabled={pagination.page >= (pagination.totalPages || 1)}
            >
              Sau
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
