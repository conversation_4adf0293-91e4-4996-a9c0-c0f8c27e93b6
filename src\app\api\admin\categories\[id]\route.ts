import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { z } from "zod";

const updateCategorySchema = z.object({
  name: z.string().min(1, "Tên danh mục không được để trống").optional(),
  description: z.string().optional(),
  imageId: z.string().optional().nullable(),
  parentId: z.string().optional().nullable(),
  slug: z.string().optional(),
});

// GET /api/admin/categories/[id] - <PERSON><PERSON><PERSON> chi tiết danh mục
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "<PERSON>hông có quyền truy cập" },
        { status: 403 }
      );
    }

    const category = await prisma.category.findUnique({
      where: { id: params.id },
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        children: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: {
            products: true,
            children: true,
          },
        },
      },
    });

    if (!category) {
      return NextResponse.json(
        { error: "Không tìm thấy danh mục" },
        { status: 404 }
      );
    }

    return NextResponse.json(category);
  } catch (error) {
    console.error("Get category error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy thông tin danh mục" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/categories/[id] - Cập nhật danh mục (Admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const data = updateCategorySchema.parse(body);

    // Check if category exists
    const existingCategory = await prisma.category.findUnique({
      where: { id: params.id },
    });

    if (!existingCategory) {
      return NextResponse.json(
        { error: "Không tìm thấy danh mục" },
        { status: 404 }
      );
    }

    // Check if parent category exists (if provided)
    if (data.parentId && data.parentId !== existingCategory.parentId) {
      // Prevent setting self as parent
      if (data.parentId === params.id) {
        return NextResponse.json(
          { error: "Không thể đặt danh mục làm cha của chính nó" },
          { status: 400 }
        );
      }

      const parentCategory = await prisma.category.findUnique({
        where: { id: data.parentId },
      });

      if (!parentCategory) {
        return NextResponse.json(
          { error: "Danh mục cha không tồn tại" },
          { status: 400 }
        );
      }

      // Check for circular reference
      const checkCircular = async (
        categoryId: string,
        targetParentId: string
      ): Promise<boolean> => {
        const category = await prisma.category.findUnique({
          where: { id: targetParentId },
          select: { parentId: true },
        });

        if (!category) return false;
        if (category.parentId === categoryId) return true;
        if (category.parentId) {
          return await checkCircular(categoryId, category.parentId);
        }
        return false;
      };

      if (await checkCircular(params.id, data.parentId)) {
        return NextResponse.json(
          { error: "Không thể tạo vòng lặp trong cấu trúc danh mục" },
          { status: 400 }
        );
      }
    }

    // Prepare update data
    let updateData: any = {};

    if (data.name !== undefined) updateData.name = data.name;
    if (data.description !== undefined)
      updateData.description = data.description;
    if (data.imageId !== undefined) updateData.imageId = data.imageId;
    if (data.parentId !== undefined) updateData.parentId = data.parentId;
    if (data.slug !== undefined) updateData.slug = data.slug;

    // Update slug if name is changed
    if (data.name && data.name !== existingCategory.name) {
      const baseSlug = data.name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .trim();

      let slug = baseSlug;
      let counter = 1;
      while (
        await prisma.category.findFirst({
          where: {
            slug: slug,
            id: { not: params.id },
          },
        })
      ) {
        slug = `${baseSlug}-${counter}`;
        counter++;
      }

      updateData.slug = slug;
    }

    const category = await prisma.category.update({
      where: { id: params.id },
      data: updateData,
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        children: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: {
            products: true,
            children: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: "Cập nhật danh mục thành công",
      category,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Update category error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật danh mục" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/categories/[id] - Xóa danh mục (Admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Check if category exists
    const existingCategory = await prisma.category.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            products: true,
            children: true,
          },
        },
      },
    });

    if (!existingCategory) {
      return NextResponse.json(
        { error: "Không tìm thấy danh mục" },
        { status: 404 }
      );
    }

    // Check if category has products
    if (existingCategory._count.products > 0) {
      return NextResponse.json(
        {
          error:
            "Không thể xóa danh mục có sản phẩm. Vui lòng di chuyển sản phẩm sang danh mục khác trước.",
        },
        { status: 400 }
      );
    }

    // Check if category has children
    if (existingCategory._count.children > 0) {
      return NextResponse.json(
        {
          error:
            "Không thể xóa danh mục có danh mục con. Vui lòng xóa danh mục con trước.",
        },
        { status: 400 }
      );
    }

    // Delete category
    await prisma.category.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      message: "Xóa danh mục thành công",
    });
  } catch (error) {
    console.error("Delete category error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xóa danh mục" },
      { status: 500 }
    );
  }
}
