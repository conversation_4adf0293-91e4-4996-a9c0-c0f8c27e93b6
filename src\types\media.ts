export type MediaType = "INTERNAL" | "EXTERNAL";

export interface MediaField {
  url?: string;
  type: MediaType;
  externalUrl?: string;
}

export interface MediaFieldFormData {
  url?: string;
  type: MediaType;
  externalUrl?: string;
}

// Helper functions for media handling
export const createMediaField = (
  url?: string,
  type: MediaType = "INTERNAL",
  externalUrl?: string
): MediaField => ({
  url,
  type,
  externalUrl,
});

// Helper function to normalize media URL (fix undefined values)
const normalizeMediaUrl = (url: string): string => {
  // If URL already contains protocol, check for undefined values
  if (url.includes("undefined")) {
    const path = url.replace(/^.*\/ns-shop-media\//, "");
    // Use hardcoded values for client-side (these should match server config)
    const endpoint = "localhost";
    const port = "9000";
    const bucketName = "ns-shop-media";
    return `http://${endpoint}:${port}/${bucketName}/${path}`;
  }
  return url;
};

export const getMediaUrl = (field: MediaField): string | undefined => {
  if (field.type === "EXTERNAL") {
    return field.externalUrl;
  }

  if (field.url) {
    return normalizeMediaUrl(field.url);
  }

  return field.url;
};

export const isValidMediaUrl = (url: string, type: MediaType): boolean => {
  if (!url) return false;

  if (type === "EXTERNAL") {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  // For internal media, just check if it's a non-empty string
  return url.length > 0;
};
