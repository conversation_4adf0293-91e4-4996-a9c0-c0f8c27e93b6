"use client";

import Link from "next/link";
import Image from "next/image";
import { useSession } from "next-auth/react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@/components/layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useEnhancedCart } from "@/contexts/enhanced-cart-context";
import {
  Minus,
  Plus,
  Trash2,
  ShoppingBag,
  ArrowLeft,
  ShoppingCart as ShoppingCartIcon,
} from "lucide-react";

export default function CartPage() {
  const { data: session } = useSession();
  const { cart, loading, updateCartItem, removeFromCart, summary } =
    useEnhancedCart();

  // Remove authentication requirement for cart page
  // Enhanced cart context supports guest carts

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(price);
  };

  const handleQuantityChange = async (itemId: string, newQuantity: number) => {
    if (newQuantity === 0) {
      await removeFromCart(itemId);
    } else {
      await updateCartItem(itemId, { quantity: newQuantity });
    }
  };

  if (!session) {
    return null; // Will redirect to signin
  }

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-8" />
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2 space-y-4">
                {Array.from({ length: 3 }, (_, i) => (
                  <Card key={i}>
                    <CardContent className="p-6">
                      <div className="flex gap-4">
                        <div className="w-20 h-20 bg-gray-200 rounded" />
                        <div className="flex-1 space-y-2">
                          <div className="h-4 bg-gray-200 rounded" />
                          <div className="h-4 bg-gray-200 rounded w-2/3" />
                          <div className="h-4 bg-gray-200 rounded w-1/2" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
              <div>
                <Card>
                  <CardContent className="p-6 space-y-4">
                    <div className="h-4 bg-gray-200 rounded" />
                    <div className="h-4 bg-gray-200 rounded" />
                    <div className="h-10 bg-gray-200 rounded" />
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="flex items-center gap-4 mb-8">
          <Link
            href="/products"
            className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            Tiếp tục mua sắm
          </Link>
        </div>

        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Giỏ hàng của bạn</h1>
          {cart && cart.items.length > 0 && (
            <p className="text-muted-foreground">
              {cart.items.length} sản phẩm trong giỏ hàng
            </p>
          )}
        </div>

        {!cart || cart.items.length === 0 ? (
          /* Empty Cart */
          <div className="text-center py-12">
            <ShoppingBag className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Giỏ hàng trống</h2>
            <p className="text-muted-foreground mb-6">
              Bạn chưa có sản phẩm nào trong giỏ hàng
            </p>
            <Link href="/products">
              <Button className="bg-pink-600 hover:bg-pink-700">
                <ShoppingCartIcon className="h-4 w-4 mr-2" />
                Bắt đầu mua sắm
              </Button>
            </Link>
          </div>
        ) : (
          /* Cart with Items */
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Sản phẩm ({cart.items.length})</CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="divide-y">
                    {cart.items.map((item) => (
                      <div key={item.id} className="p-6">
                        <div className="flex gap-4">
                          {/* Product Image */}
                          <Link href={`/products/${item.product.slug}`}>
                            <div className="relative w-20 h-20 flex-shrink-0 overflow-hidden rounded-lg bg-gray-100">
                              <Image
                                src={
                                  item.product.images[0] ||
                                  "/images/placeholder.jpg"
                                }
                                alt={item.product.name}
                                fill
                                className="object-cover hover:scale-105 transition-transform duration-200"
                              />
                            </div>
                          </Link>

                          {/* Product Info */}
                          <div className="flex-1 min-w-0">
                            <Link href={`/products/${item.product.slug}`}>
                              <h3 className="font-medium text-sm mb-1 hover:text-pink-600 transition-colors line-clamp-2">
                                {item.product.name}
                              </h3>
                            </Link>

                            <div className="flex items-center gap-2 mb-2">
                              {item.product.salePrice ? (
                                <>
                                  <span className="font-semibold text-pink-600">
                                    {formatPrice(item.product.salePrice)}
                                  </span>
                                  <span className="text-sm text-muted-foreground line-through">
                                    {formatPrice(item.product.price)}
                                  </span>
                                </>
                              ) : (
                                <span className="font-semibold">
                                  {formatPrice(item.product.price)}
                                </span>
                              )}
                            </div>

                            {/* Stock Status */}
                            {item.product.stock < item.quantity && (
                              <p className="text-sm text-red-600 mb-2">
                                Chỉ còn {item.product.stock} sản phẩm
                              </p>
                            )}

                            {item.product.status !== "ACTIVE" && (
                              <p className="text-sm text-red-600 mb-2">
                                Sản phẩm không còn khả dụng
                              </p>
                            )}

                            {/* Quantity Controls */}
                            <div className="flex items-center gap-2">
                              <div className="flex items-center border rounded-lg">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    handleQuantityChange(
                                      item.id,
                                      item.quantity - 1
                                    )
                                  }
                                  disabled={item.quantity <= 1}
                                  className="h-8 w-8 p-0"
                                >
                                  <Minus className="h-3 w-3" />
                                </Button>
                                <span className="w-12 text-center text-sm font-medium">
                                  {item.quantity}
                                </span>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    handleQuantityChange(
                                      item.id,
                                      item.quantity + 1
                                    )
                                  }
                                  disabled={item.quantity >= item.product.stock}
                                  className="h-8 w-8 p-0"
                                >
                                  <Plus className="h-3 w-3" />
                                </Button>
                              </div>

                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeFromCart(item.id)}
                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>

                          {/* Item Total */}
                          <div className="text-right">
                            <div className="font-semibold">
                              {formatPrice(
                                (item.product.salePrice || item.product.price) *
                                  item.quantity
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Order Summary */}
            <div>
              <Card className="sticky top-4">
                <CardHeader>
                  <CardTitle>Tóm tắt đơn hàng</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>
                        Tạm tính ({summary?.totalQuantity || 0} sản phẩm)
                      </span>
                      <span>{formatPrice(summary?.subtotal || 0)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Phí vận chuyển</span>
                      <span className="text-green-600">Miễn phí</span>
                    </div>
                    <div className="border-t pt-2">
                      <div className="flex justify-between font-semibold">
                        <span>Tổng cộng</span>
                        <span className="text-pink-600">
                          {formatPrice(summary?.total || 0)}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Link href="/checkout">
                      <Button className="w-full bg-pink-600 hover:bg-pink-700">
                        Tiến hành thanh toán
                      </Button>
                    </Link>
                    <Link href="/products">
                      <Button variant="outline" className="w-full">
                        Tiếp tục mua sắm
                      </Button>
                    </Link>
                  </div>

                  {/* Benefits */}
                  <div className="pt-4 border-t space-y-2 text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                      <span>Miễn phí vận chuyển toàn quốc</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                      <span>Đổi trả trong 30 ngày</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-purple-500 rounded-full" />
                      <span>Bảo hành chính hãng</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
}
