import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/lib/admin-auth";
import { MediaService } from "@/lib/services/media.service";

// GET - Get product media
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminToken(request);
    if (!authResult || !authResult.success) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const result = await MediaService.getProductMedia(params.id);

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data,
      });
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Get product media error:", error);
    return NextResponse.json(
      { success: false, error: "<PERSON><PERSON> lỗi x<PERSON>y ra khi tải media sản phẩm" },
      { status: 500 }
    );
  }
}

// POST - Add media to product
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminToken(request);
    if (!authResult || !authResult.success) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { mediaId, order = 0, isPrimary = false } = body;

    if (!mediaId) {
      return NextResponse.json(
        { success: false, error: "Media ID is required" },
        { status: 400 }
      );
    }

    const result = await MediaService.addToProduct(
      params.id,
      mediaId,
      order,
      isPrimary
    );

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data,
      });
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Add product media error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi thêm media vào sản phẩm" },
      { status: 500 }
    );
  }
}

// DELETE - Remove media from product
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminToken(request);
    if (!authResult || !authResult.success) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const mediaId = searchParams.get("mediaId");

    if (!mediaId) {
      return NextResponse.json(
        { success: false, error: "Media ID is required" },
        { status: 400 }
      );
    }

    const result = await MediaService.removeFromProduct(params.id, mediaId);

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: result.message,
      });
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Remove product media error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi xóa media khỏi sản phẩm" },
      { status: 500 }
    );
  }
}
