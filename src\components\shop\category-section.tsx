"use client";

import Link from "next/link";
import { <PERSON>R<PERSON>, Loader2, Al<PERSON>Circle } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { CategoryImage } from "@/components/ui/client-image";
import { useRootCategories } from "@/hooks/use-categories";

// Helper function to get category image
function getCategoryImage(category: any): string {
  // Check for new media relationship first
  if (category.image?.url) {
    return category.image.url;
  }

  // Fallback to imageId or default
  return `/images/categories/${category.slug}.svg`;
}

// Color gradients for categories (fallback styling)
const colorGradients = [
  "from-blue-400 to-blue-600",
  "from-pink-400 to-pink-600",
  "from-indigo-400 to-indigo-600",
  "from-purple-400 to-purple-600",
  "from-green-400 to-green-600",
  "from-orange-400 to-orange-600",
  "from-red-400 to-red-600",
  "from-yellow-400 to-yellow-600",
];

export function CategorySection() {
  const { categories, loading, error } = useRootCategories();
  return (
    <section className="py-16 lg:py-24">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            Danh mục sản phẩm
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Khám phá bộ sưu tập đa dạng của chúng tôi với nhiều danh mục thời
            trang khác nhau
          </p>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-muted-foreground">
              Đang tải danh mục...
            </span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="flex justify-center items-center py-12">
            <AlertCircle className="h-8 w-8 text-red-500" />
            <span className="ml-2 text-red-500">
              Có lỗi xảy ra khi tải danh mục
            </span>
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && categories.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">Chưa có danh mục nào</p>
          </div>
        )}

        {/* Categories Grid */}
        {!loading && !error && categories.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 lg:gap-6">
            {categories.slice(0, 6).map((category, index) => (
              <Link key={category.id} href={`/categories/${category.slug}`}>
                <Card className="group cursor-pointer border-0 bg-transparent">
                  <CardContent className="p-0">
                    <div className="relative overflow-hidden rounded-xl">
                      {/* Category Image */}
                      <div className="aspect-square relative">
                        <CategoryImage
                          src={getCategoryImage(category)}
                          alt={category.name}
                          fill
                          className="object-cover transition-transform duration-300 group-hover:scale-105"
                        />
                        {/* Overlay */}
                        <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300" />

                        {/* Category Info */}
                        <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                          <h3 className="font-semibold text-sm lg:text-base mb-1">
                            {category.name}
                          </h3>
                          <p className="text-xs text-white/90">
                            {(category as any)._count?.products || 0} sản phẩm
                          </p>
                        </div>

                        {/* Hover Arrow */}
                        <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <ArrowRight className="h-4 w-4 text-white" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        )}

        {/* View All Button */}
        {!loading && !error && categories.length > 0 && (
          <div className="text-center mt-12">
            <Link
              href="/categories"
              className="inline-flex items-center space-x-2 text-fashion-600 hover:text-fashion-700 font-medium transition-colors"
            >
              <span>Xem tất cả danh mục</span>
              <ArrowRight className="h-4 w-4" />
            </Link>
          </div>
        )}
      </div>
    </section>
  );
}
