import React from 'react';
import { renderHook, waitFor, act } from '@testing-library/react';
import { EnhancedCartProvider, useEnhancedCart } from '../enhanced-cart-context';
import { useCart, useQuickAddToCart, useCartValidation } from '@/hooks';

// Mock the hooks
jest.mock('@/hooks', () => ({
  useCart: jest.fn(),
  useQuickAddToCart: jest.fn(),
  useCartValidation: jest.fn(),
}));

const mockUseCart = useCart as jest.MockedFunction<typeof useCart>;
const mockUseQuickAddToCart = useQuickAddToCart as jest.MockedFunction<typeof useQuickAddToCart>;
const mockUseCartValidation = useCartValidation as jest.MockedFunction<typeof useCartValidation>;

// Mock data
const mockCartItem = {
  id: '1',
  productId: 'product-1',
  quantity: 2,
  product: {
    id: 'product-1',
    name: 'Test Product',
    slug: 'test-product',
    price: 100000,
    salePrice: 80000,
    images: ['image1.jpg'],
    stock: 10,
    category: { id: '1', name: 'Test Category', slug: 'test-category' },
  },
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

const mockCart = {
  id: 'cart-1',
  items: [mockCartItem],
  total: 160000,
  itemCount: 1,
  totalQuantity: 2,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

const mockCartHook = {
  cart: mockCart,
  loading: false,
  error: null,
  addToCart: jest.fn(),
  updateCartItem: jest.fn(),
  removeFromCart: jest.fn(),
  clearCart: jest.fn(),
  refetch: jest.fn(),
};

const mockQuickAddHook = {
  quickAdd: jest.fn(),
  loading: false,
};

const mockValidationHook = {
  isValid: true,
  invalidItems: [],
  errors: [],
  loading: false,
};

// Wrapper component for testing
const wrapper = ({ children }: { children: React.ReactNode }) => (
  <EnhancedCartProvider>{children}</EnhancedCartProvider>
);

describe('EnhancedCartContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseCart.mockReturnValue(mockCartHook);
    mockUseQuickAddToCart.mockReturnValue(mockQuickAddHook);
    mockUseCartValidation.mockReturnValue(mockValidationHook);
  });

  it('should provide cart data', async () => {
    const { result } = renderHook(() => useEnhancedCart(), { wrapper });

    expect(result.current.cart).toEqual(mockCart);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it('should provide cart summary', async () => {
    const { result } = renderHook(() => useEnhancedCart(), { wrapper });

    expect(result.current.summary).toEqual({
      subtotal: 160000,
      tax: 16000, // 10% tax
      shipping: 30000, // Default shipping
      total: 206000,
      itemCount: 1,
      totalQuantity: 2,
    });
  });

  it('should handle empty cart summary', async () => {
    const emptyCart = { ...mockCart, items: [], total: 0, itemCount: 0, totalQuantity: 0 };
    mockUseCart.mockReturnValue({ ...mockCartHook, cart: emptyCart });

    const { result } = renderHook(() => useEnhancedCart(), { wrapper });

    expect(result.current.summary).toEqual({
      subtotal: 0,
      tax: 0,
      shipping: 0,
      total: 0,
      itemCount: 0,
      totalQuantity: 0,
    });
  });

  it('should provide cart actions', async () => {
    const { result } = renderHook(() => useEnhancedCart(), { wrapper });

    expect(typeof result.current.addToCart).toBe('function');
    expect(typeof result.current.updateCartItem).toBe('function');
    expect(typeof result.current.removeFromCart).toBe('function');
    expect(typeof result.current.clearCart).toBe('function');
    expect(typeof result.current.refetch).toBe('function');
  });

  it('should provide quick add functionality', async () => {
    const { result } = renderHook(() => useEnhancedCart(), { wrapper });

    expect(typeof result.current.quickAdd).toBe('function');
    expect(result.current.quickAddLoading).toBe(false);
  });

  it('should provide validation data', async () => {
    const { result } = renderHook(() => useEnhancedCart(), { wrapper });

    expect(result.current.isValid).toBe(true);
    expect(result.current.validationErrors).toEqual([]);
  });

  it('should handle validation errors', async () => {
    const invalidValidation = {
      isValid: false,
      invalidItems: [{ itemId: '1', message: 'Out of stock', type: 'stock' as const }],
      errors: ['Product out of stock'],
      loading: false,
    };
    mockUseCartValidation.mockReturnValue(invalidValidation);

    const { result } = renderHook(() => useEnhancedCart(), { wrapper });

    expect(result.current.isValid).toBe(false);
    expect(result.current.validationErrors).toEqual([
      { itemId: '1', message: 'Out of stock', type: 'stock' }
    ]);
  });

  it('should provide utility functions', async () => {
    const { result } = renderHook(() => useEnhancedCart(), { wrapper });

    // Test getItemQuantity
    expect(result.current.getItemQuantity('product-1')).toBe(2);
    expect(result.current.getItemQuantity('product-2')).toBe(0);

    // Test isInCart
    expect(result.current.isInCart('product-1')).toBe(true);
    expect(result.current.isInCart('product-2')).toBe(false);

    // Test canAddToCart
    expect(result.current.canAddToCart('product-1', 5)).toBe(true);
    expect(result.current.canAddToCart('product-1', 15)).toBe(false); // Exceeds stock
  });

  it('should handle null cart', async () => {
    mockUseCart.mockReturnValue({ ...mockCartHook, cart: null });

    const { result } = renderHook(() => useEnhancedCart(), { wrapper });

    expect(result.current.cart).toBe(null);
    expect(result.current.summary).toBe(null);
    expect(result.current.getItemQuantity('product-1')).toBe(0);
    expect(result.current.isInCart('product-1')).toBe(false);
    expect(result.current.canAddToCart('product-1', 1)).toBe(true);
  });

  it('should handle loading state', async () => {
    mockUseCart.mockReturnValue({ ...mockCartHook, loading: true });

    const { result } = renderHook(() => useEnhancedCart(), { wrapper });

    expect(result.current.loading).toBe(true);
  });

  it('should handle error state', async () => {
    const errorMessage = 'Failed to load cart';
    mockUseCart.mockReturnValue({ ...mockCartHook, error: errorMessage });

    const { result } = renderHook(() => useEnhancedCart(), { wrapper });

    expect(result.current.error).toBe(errorMessage);
  });

  it('should call cart actions', async () => {
    const { result } = renderHook(() => useEnhancedCart(), { wrapper });

    await act(async () => {
      await result.current.addToCart({ productId: 'product-2', quantity: 1 });
    });

    expect(mockCartHook.addToCart).toHaveBeenCalledWith({
      productId: 'product-2',
      quantity: 1,
    });

    await act(async () => {
      await result.current.updateCartItem('1', { quantity: 3 });
    });

    expect(mockCartHook.updateCartItem).toHaveBeenCalledWith('1', { quantity: 3 });

    await act(async () => {
      await result.current.removeFromCart('1');
    });

    expect(mockCartHook.removeFromCart).toHaveBeenCalledWith('1');

    await act(async () => {
      await result.current.clearCart();
    });

    expect(mockCartHook.clearCart).toHaveBeenCalled();

    act(() => {
      result.current.refetch();
    });

    expect(mockCartHook.refetch).toHaveBeenCalled();
  });

  it('should call quick add', async () => {
    const { result } = renderHook(() => useEnhancedCart(), { wrapper });

    await act(async () => {
      await result.current.quickAdd('product-2', 2);
    });

    expect(mockQuickAddHook.quickAdd).toHaveBeenCalledWith('product-2', 2);
  });

  it('should throw error when used outside provider', () => {
    const { result } = renderHook(() => useEnhancedCart());

    expect(result.error).toEqual(
      Error('useEnhancedCart must be used within an EnhancedCartProvider')
    );
  });
});
